<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>创建用户</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
</head>
<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <form class="layui-form layui-form-pane" lay-filter="createUserForm">
                <fieldset>
                    <legend>创建用户</legend>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label required">用户名</label>
                        <div class="layui-input-block">
                            <input type="text" name="username" lay-verify="required" placeholder="请输入用户名" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label required">密码</label>
                        <div class="layui-input-block">
                            <input type="password" name="password" lay-verify="required" placeholder="请输入密码" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="createUser">立即创建</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </div>
                </fieldset>
            </form>
        </div>
    </div>

    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script>
        layui.use(['form', 'layer'], function() {
            const form = layui.form;
            const layer = layui.layer;

            // 表单提交
            form.on('submit(createUser)', function(data) {
                const loading = layer.load(1);
                
                axios.post('/api/createUser', data.field)
                    .then(response => {
                        layer.close(loading);
                        if (response.data.code === 0) {
                            layer.msg('用户创建成功', { icon: 1 });
                            form.val('createUserForm', {
                                username: '',
                                password: ''
                            });
                        } else {
                            layer.msg(response.data.msg, { icon: 2 });
                        }
                    })
                    .catch(error => {
                        layer.close(loading);
                        layer.msg('创建失败: ' + error.message, { icon: 2 });
                    });
                
                return false;
            });
        });
    </script>
</body>
</html>
