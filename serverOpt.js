const adminName = "cjroot"//超级管理员账户名 部署以后不允许修改
const appName = "测试后台"//后台名字
const maxDeviceNum = 500//最大控数
const maxLevel = 1//最大代理级别0证明无代理,1证明能开1级代理,以此类推
// const serverPort = 16666//服务端口
const serverPort = 15001//服务端口
const dbName = "ceshinew"//数据库名字
//--------------以上参数按需配置--------------

const path = require('path');
let serverUrl = "http://127.0.0.1"
module.exports = {
    adminName,
    maxDeviceNum,
    maxLevel,
    serverPort,
    mongoUrl: `mongodb://127.0.0.1:27017/${dbName}`,
    development: process.env.NODE_ENV == "development" ? true : false,
    publicPath: path.join(__dirname, 'public'),//静态目录
    getServerUrl: function () { return serverUrl },//获取本地或公网ip
    setServerUrl: function (url) { serverUrl = url },
    ppxxxkkk: deobfuscateString(),
    appName: appName,
    localDate: function (v) { const d = new Date(v || Date.now()); d.setMinutes(d.getMinutes() - d.getTimezoneOffset()); return d.toISOString(); },
    layuiMenu: { "homeInfo": { "title": "首页", "href": "page/welcome-3.html" }, "logoInfo": { "title": appName, "image": "images/logo.png", "href": "" }, "menuInfo": [{ "title": "学习测试", "icon": "fa fa-address-book", "href": "", "target": "_self", "child": [] }] }
};

function deobfuscateString() {
    const obfuscatedStr = `k1{9=>91^@4&7Ablk$-eg'_%&dt/"t;<`
    let originalStr = '';
    for (let i = 0; i < obfuscatedStr.length; i++) {
        const obfuscatedCharCode = obfuscatedStr.charCodeAt(i);
        const charCode = obfuscatedCharCode - 1; // 每个字符的字符码减1
        originalStr += String.fromCharCode(charCode);
    }
    return originalStr;
}

