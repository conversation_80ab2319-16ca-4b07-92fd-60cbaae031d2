const express = require('express')
const router = express.Router();

const mongoose = require('../db/db.js');
const deviceShe = require('../db/deviceShe')
const groupShe = require('../db/groupShe')

const debug = require('debug')('a-sokio-yun:server');

router.get('/', async function (req, res) {
    let userName = req.session.userName
    if (!userName) {
        return res.json({
            msg: "请刷新浏览器重新登录"
        })
    }
    let groupMod = mongoose.model('group_' + userName, groupShe)
    const docs = await groupMod.find({})
    res.json({
        "code": 0,
        "msg": "",
        "count": docs.length,
        "data": docs
    })
});

router.get('/addGroup', async function (req, res) {
    let userName = req.session.userName
    if (!userName) {
        return res.json({
            msg: "请刷新浏览器重新登录"
        })
    }
    let groupMod = mongoose.model('group_' + userName, groupShe)
    let groupName = req.query.groupName

    try {
        await groupMod.create({ groupName })
        const docs = await groupMod.find({})
        res.json({
            "code": 0,
            "msg": "",
            "count": docs.length,
            "data": docs
        })
    } catch (error) {
        res.json({
            msg: "[" + groupName + "]错误->" + err.message
        })
    }
})

router.post('/addDevices', function (req, res) {
    let userName = req.session.userName
    if (!userName) {
        return res.json({
            msg: "请刷新浏览器重新登录"
        })
    }
    let deviceMod = mongoose.model('device_' + userName, deviceShe)
    // let groupMod = mongoose.model('group_' + userName, groupShe)
    let { deviceIdArr, groupId } = req.body

    deviceIdArr.forEach(deviceId => {
        deviceMod.updateOne({ _id: deviceId }, { groupId: groupId }).then()
    });

    res.json({
        msg: "添加成功"
    })

})

router.get('/delGroup', async function (req, res) {
    let userName = req.session.userName
    if (!userName) {
        return res.json({
            msg: "请刷新浏览器重新登录"
        })
    }

    try {
        let _id = req.query._id
        let groupMod = mongoose.model('group_' + userName, groupShe)
        let deviceMod = mongoose.model('device_' + userName, deviceShe)
        await deviceMod.updateMany({ groupId: _id }, { groupId: null })
        await groupMod.deleteOne({ _id: _id })
        const docs = await groupMod.find({})
        res.json({
            "code": 0,
            "msg": "",
            "count": docs.length,
            "data": docs
        })
    } catch (error) {
        res.json({
            msg: "删除分组错误:" + error.message
        })
    }
});

router.get('/editGroupName', async function (req, res) {
    let userName = req.session.userName
    if (!userName) {
        return res.json({
            msg: "请刷新浏览器重新登录"
        })
    }

    let groupMod = mongoose.model('group_' + userName, groupShe)
    let groupName = req.query.groupName
    let _id = req.query._id

    try {
        await groupMod.updateOne({ _id }, { groupName })
        const docs = await groupMod.find({})
        res.json({
            "code": 0,
            "msg": "",
            "count": docs.length,
            "data": docs
        })
    } catch (error) {
        res.json({
            msg: "错误:" + error.message
        })
    }
})



module.exports = router;


