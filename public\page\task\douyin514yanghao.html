<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui优化版</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
    <style>
        .image-preview {
            margin-top: 10px;
            display: flex;
            gap: 10px;
        }

        .preview-item {
            position: relative;
            width: 100px;
            height: 100px;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
        }

        .preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .delete-btn {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            background: #ff4444;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 20px;
            cursor: pointer;
        }

        /* 模式选择样式 */
        .mode-switch-buttons {
            margin: 10px 0;
            padding: 15px;
            background-color: #f8f8f8;
            border-radius: 5px;
            border: 1px solid #e6e6e6;
        }

        .mode-switch-buttons .layui-btn {
            margin: 0 10px;
            min-width: 120px;
        }

        .mode-switch-buttons .layui-btn-disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .current-mode-display {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
            text-align: center;
        }

        .task-config-section {
            display: none;
        }

        .task-config-section.active {
            display: block;
        }
    </style>
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <form class="layui-form layui-form-pane" action="">
                <fieldset style="border-color: blue;border-width: 3px; background-color: rgb(255, 252, 194);">
                    <legend style="color: blue;font-weight: bold;font-size: 16px;">注意：请选择分组(点击全选,双击展开组内设备,可单选可多选) <input id="inputaaa" />
                    </legend>
                    <input type="checkbox" lay-filter="quanXuan1" title="全选" />
                    <br>
                    <br>
                    <div class="layui-form-item" id="groups1"></div>
                </fieldset>
                
                <!-- 隐藏字段存储当前模式 -->
                <input type="hidden" id="currentMode" value="yangHao">

                <fieldset>
                    <legend>【任务模式选择】</legend>

                    <div class="layui-form-item">
                        <label class="layui-form-label">选择模式</label>
                        <div class="layui-input-block">
                            <!-- 模式切换按钮 -->
                            <div class="mode-switch-buttons">
                                <button type="button" class="layui-btn layui-btn-normal layui-btn-sm" id="yangHaoBtn" onclick="switchMode('yangHao')">
                                    <i class="layui-icon layui-icon-user"></i> 养号模式
                                </button>
                                <button type="button" class="layui-btn layui-btn-warm layui-btn-sm layui-btn-disabled" id="videoWatchBtn" onclick="switchMode('videoWatch')">
                                    <i class="layui-icon layui-icon-play"></i> 视频观看
                                </button>
                                <div class="current-mode-display">
                                    <span id="currentModeText" style="font-weight: bold; color: #1E9FFF;">当前模式：养号模式</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </fieldset>

                <!-- 养号模式配置 -->
                <fieldset id="yangHaoConfig" class="task-config-section active">
                    <legend>【养号任务设置】</legend>

                    <div class="layui-form-item">
                        <label class="layui-form-label">刷视频时间</label>
                        <div class="layui-input-inline" style="width: 100px;">
                            <input type="number" id="minTime" name="minTime" placeholder="最小" class="layui-input">
                        </div>
                        <div class="layui-form-mid">-</div>
                        <div class="layui-input-inline" style="width: 100px;">
                            <input type="number" id="maxTime" name="maxTime" placeholder="最大" class="layui-input">
                        </div>
                        <div class="layui-form-mid">秒/视频</div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">总任务时间</label>
                        <div class="layui-input-inline" style="width: 120px;">
                            <input type="number" id="totalTime" name="totalTime" placeholder="分钟" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">操作概率</label>
                        <div class="layui-input-inline" style="width: 120px;">
                            <input type="number" id="probability" name="probability" placeholder="1-100" class="layui-input">
                        </div>
                        <div class="layui-form-mid">%</div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-block">
                            <textarea id="keyword" name="keyword" placeholder="多个关键词请用换行分隔" class="layui-textarea"></textarea>
                        </div>
                    </div>
                </fieldset>

                <!-- 视频观看模式配置 -->
                <fieldset id="videoWatchConfig" class="task-config-section">
                    <legend>【视频观看设置】</legend>

                    <div class="layui-form-item">
                        <label class="layui-form-label">操作类型</label>
                        <div class="layui-input-block">
                            <input type="radio" name="videoAction" value="like" title="点赞" checked>
                            <input type="radio" name="videoAction" value="collect" title="收藏">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">观看个数</label>
                        <div class="layui-input-inline" style="width: 120px;">
                            <input type="number" id="watchCount" name="watchCount" placeholder="每轮观看数量" class="layui-input">
                        </div>
                        <div class="layui-form-mid">个/轮</div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">循环轮数</label>
                        <div class="layui-input-inline" style="width: 120px;">
                            <input type="number" id="loopCount" name="loopCount" placeholder="循环次数" class="layui-input">
                        </div>
                        <div class="layui-form-mid">轮</div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">视频链接</label>
                        <div class="layui-input-block">
                            <textarea id="videoLinks" name="videoLinks" placeholder="多个视频链接请用换行分隔" class="layui-textarea"></textarea>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">观看时长</label>
                        <div class="layui-input-inline" style="width: 100px;">
                            <input type="number" id="minWatchTime" name="minWatchTime" placeholder="最小" class="layui-input">
                        </div>
                        <div class="layui-form-mid">-</div>
                        <div class="layui-input-inline" style="width: 100px;">
                            <input type="number" id="maxWatchTime" name="maxWatchTime" placeholder="最大" class="layui-input">
                        </div>
                        <div class="layui-form-mid">秒/视频</div>
                    </div>
                </fieldset>

                <!-- 执行按钮 -->
                <fieldset>
                    <legend>【任务执行】</legend>
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn layui-btn-sm" id="executeTaskBtn" value="养号" lay-submit="" lay-filter="tijiao">
                                <i class="layui-icon layui-icon-user"></i> 执行养号任务
                            </button>
                            <button class="layui-btn layui-btn-sm layui-btn-danger" lay-submit="" lay-filter="stopTask">
                                <i class="layui-icon layui-icon-pause"></i> 停止任务
                            </button>
                        </div>
                    </div>
                </fieldset>
            </form>
        </div>
    </div>

    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script src="/js/lay-config.js?v=2.0.4" charset="utf-8"></script>
    <script>
        layui.use(['tongYong', 'form'], function () {
            var tongYong = layui.tongYong;
            var form = layui.form;
            tongYong.tongYong1();
            form.render();
        });

        // 模式切换功能
        function switchMode(mode) {
            const currentModeInput = document.getElementById('currentMode');
            const currentModeText = document.getElementById('currentModeText');
            const yangHaoBtn = document.getElementById('yangHaoBtn');
            const videoWatchBtn = document.getElementById('videoWatchBtn');
            const executeTaskBtn = document.getElementById('executeTaskBtn');

            const yangHaoConfig = document.getElementById('yangHaoConfig');
            const videoWatchConfig = document.getElementById('videoWatchConfig');

            // 更新当前模式
            currentModeInput.value = mode;

            // 更新按钮样式和显示
            if (mode === 'yangHao') {
                yangHaoBtn.className = 'layui-btn layui-btn-normal layui-btn-sm';
                videoWatchBtn.className = 'layui-btn layui-btn-warm layui-btn-sm layui-btn-disabled';
                currentModeText.textContent = '当前模式：养号模式';
                currentModeText.style.color = '#1E9FFF';

                // 显示/隐藏配置区域
                yangHaoConfig.classList.add('active');
                videoWatchConfig.classList.remove('active');

                // 更新执行按钮
                executeTaskBtn.value = '养号';
                executeTaskBtn.innerHTML = '<i class="layui-icon layui-icon-user"></i> 执行养号任务';
            } else {
                yangHaoBtn.className = 'layui-btn layui-btn-normal layui-btn-sm layui-btn-disabled';
                videoWatchBtn.className = 'layui-btn layui-btn-warm layui-btn-sm';
                currentModeText.textContent = '当前模式：视频观看';
                currentModeText.style.color = '#FFB800';

                // 显示/隐藏配置区域
                yangHaoConfig.classList.remove('active');
                videoWatchConfig.classList.add('active');

                // 更新执行按钮
                executeTaskBtn.value = '视频观看';
                executeTaskBtn.innerHTML = '<i class="layui-icon layui-icon-play"></i> 执行视频观看任务';
            }

            console.log('模式已切换到:', mode, mode === 'yangHao' ? '养号模式' : '视频观看模式');
        }

        // 生成任务ID
        function generateTaskId(mode) {
            const userName = localStorage.getItem("userName") || "default";
            const timestamp = Date.now();
            const modePrefix = mode === 'yangHao' ? 'YANGHAO' : 'VIDEOWATCH';
            return `${modePrefix}_${userName}_${timestamp}`;
        }

        // 重写tongYong的btn_renWu函数以支持视频观看模式
        layui.use(['form'], function() {
            var form = layui.form;

            // 监听表单提交，但阻止默认的tongYong处理
            form.on('submit(tijiao)', function(data) {
                console.log('表单提交数据:', data);

                // 获取当前模式
                const currentMode = document.getElementById('currentMode').value;

                // 更新按钮的value属性
                data.elem.value = currentMode === 'yangHao' ? '养号' : '视频观看';

                if (currentMode === 'yangHao') {
                    // 养号模式：让tongYong正常处理
                    return false; // 让tongYong的默认处理继续
                } else {
                    // 视频观看模式：自定义处理
                    executeVideoWatchTask(data);
                    return false; // 阻止表单默认提交
                }
            });
        });



        // 执行视频观看任务（使用tongYong兼容的格式）
        function executeVideoWatchTask(formSubmitData) {
            // 获取视频操作类型
            const videoAction = document.querySelector('input[name="videoAction"]:checked').value;

            // 从表单数据中收集参数
            const formData = formSubmitData.field;

            // 参数验证
            if (!formData.watchCount || !formData.loopCount || !formData.videoLinks) {
                return layui.layer.msg('请填写观看个数、循环轮数和视频链接');
            }
            if (!formData.minWatchTime || !formData.maxWatchTime) {
                return layui.layer.msg('请填写观看时长范围');
            }
            if (parseInt(formData.watchCount) <= 0 || parseInt(formData.loopCount) <= 0) {
                return layui.layer.msg('观看个数和循环轮数必须大于0');
            }
            if (parseInt(formData.minWatchTime) <= 0 || parseInt(formData.maxWatchTime) <= 0) {
                return layui.layer.msg('观看时长必须大于0');
            }
            if (parseInt(formData.minWatchTime) >= parseInt(formData.maxWatchTime)) {
                return layui.layer.msg('最小观看时长必须小于最大观看时长');
            }

            // 处理视频链接为数组格式
            const videoLinksArray = formData.videoLinks.split('\n')
                .filter(link => link.trim() !== '')
                .map(link => link.trim());

            if (videoLinksArray.length === 0) {
                return layui.layer.msg('请至少输入一个视频链接');
            }

            // 验证设备选择
            const groupsCheck = JSON.parse(document.getElementById('inputaaa').value || '{}');
            const hasSelected = Object.values(groupsCheck).some(selected => selected);

            if (!hasSelected) {
                console.log('groupsCheck:', groupsCheck);
                return layui.layer.msg('请至少选择一个设备');
            }

            // 构建符合tongYong格式的任务数据
            const taskData = {
                // 视频观看的特殊参数
                watchCount: parseInt(formData.watchCount),
                loopCount: parseInt(formData.loopCount),
                videoLinks: videoLinksArray,
                minWatchTime: parseInt(formData.minWatchTime),
                maxWatchTime: parseInt(formData.maxWatchTime),
                videoAction: videoAction,
                actionDisplayName: videoAction === 'like' ? '点赞' : '收藏',
                // 添加模式标识
                taskMode: 'videoWatch'
            };

            // 构建提交数据（参考tongYong.js的格式）
            const submitData = {
                taskName: '视频观看',
                taskData: taskData,
                userName: localStorage.getItem("userName") || "",
                timeStamp: Date.now(),
                socketIdArr: Object.keys(groupsCheck).filter(key => groupsCheck[key])
            };

            console.log('=== 视频观看任务提交详情 ===');
            console.log('操作类型:', videoAction === 'like' ? '点赞' : '收藏');
            console.log('观看个数:', formData.watchCount);
            console.log('循环轮数:', formData.loopCount);
            console.log('视频链接数量:', videoLinksArray.length);
            console.log('观看时长范围:', formData.minWatchTime + '-' + formData.maxWatchTime + '秒');
            console.log('视频链接列表:', videoLinksArray);
            console.log('提交数据:', JSON.stringify(submitData, null, 2));
            console.log('==================');

            // 确认提交
            layui.layer.confirm(`执行[视频观看]的任务??`, function (index) {
                layui.layer.close(index);

                // 使用与tongYong相同的提交方式
                axios.post("/wsRouter/faTast", { type: "任务", data: submitData })
                    .then((res) => {
                        layui.layer.msg(res.data.msg);
                    })
                    .catch((e) => {
                        layui.layer.alert(e.message);
                    });
            });
        }



        // 停止任务按钮（使用tongYong的停止任务处理）
        layui.use(['form'], function() {
            var form = layui.form;

            // 监听停止任务
            form.on('submit(stopTask)', function(data) {
                layui.layer.confirm('确定要停止当前任务吗？', { icon: 3, title: '提示' }, function (index) {
                    const currentMode = document.getElementById('currentMode').value;
                    const taskName = currentMode === 'yangHao' ? '养号任务' : '视频观看任务';

                    // 这里可以添加停止任务的逻辑
                    layui.layer.msg(taskName + '已停止', { icon: 5 });
                    layui.layer.close(index);
                });
                return false;
            });
        });

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认选择养号模式
            switchMode('yangHao');
        });
    </script>
</body>

</html>