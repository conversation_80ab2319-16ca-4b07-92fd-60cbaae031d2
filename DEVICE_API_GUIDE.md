# 设备数据上报API接口文档

## 概述

本文档专门为设备端开发者提供数据上报接口说明。所有接口都包含设备认证和自动绑定功能。

## 服务器信息

- **服务器地址**: `43.143.83.131`
- **端口**: `15001`
- **基础URL**: `http://43.143.83.131:15001`

## 核心接口

### 1. 设备数据上报接口 ⭐ (推荐)

**接口地址**: `POST /wsRouter/device/report`

**功能**: 单个设备上报任务执行数据，自动处理设备认证和绑定

**请求参数**:
```json
{
    "device_name": "device_001",           // 设备名称 (必填)
    "user_name": "cjroot",                 // 用户名 (必填)
    "device_uuid": "uuid-1234-5678",       // 设备UUID (可选)
    "task_type": "like",                   // 任务类型 (必填)
    "success": true,                       // 是否成功 (必填)
    "execution_data": {                    // 执行数据 (可选)
        "target_url": "https://example.com/video/123",
        "duration": 1500,
        "additional_info": "点赞成功"
    },
    "timestamp": "2025-01-09T10:30:00Z"    // 时间戳 (可选)
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "数据上报成功",
    "data": {
        "device_name": "device_001",
        "user_name": "cjroot",
        "task_type": "like",
        "status": "completed",
        "timestamp": "2025-01-09T10:30:00.000Z"
    }
}
```

### 2. 设备批量数据上报接口

**接口地址**: `POST /wsRouter/device/batchReport`

**功能**: 批量上报多个任务执行数据，适用于高并发场景

**请求参数**:
```json
{
    "user_name": "cjroot",
    "reports": [
        {
            "device_name": "device_001",
            "task_type": "like",
            "success": true,
            "execution_data": {
                "target_url": "https://example.com/video/123",
                "duration": 1500
            },
            "timestamp": "2025-01-09T10:30:00Z"
        },
        {
            "device_name": "device_002",
            "task_type": "collect",
            "success": false,
            "execution_data": {
                "error_message": "网络超时",
                "retry_count": 3
            },
            "timestamp": "2025-01-09T10:30:05Z"
        }
    ]
}
```

### 3. 设备注册接口

**接口地址**: `POST /wsRouter/device/register`

**功能**: 手动注册新设备到指定用户

**请求参数**:
```json
{
    "device_name": "device_001",           // 设备名称 (必填)
    "user_name": "cjroot",                 // 用户名 (必填)
    "device_uuid": "uuid-1234-5678",       // 设备UUID (可选)
    "device_width": 1080,                  // 设备宽度 (可选)
    "device_height": 1920                  // 设备高度 (可选)
}
```

### 4. 设备心跳接口

**接口地址**: `POST /wsRouter/device/heartbeat`

**功能**: 设备定期发送心跳，保持在线状态

**请求参数**:
```json
{
    "device_name": "device_001",           // 设备名称 (必填)
    "user_name": "cjroot",                 // 用户名 (必填)
    "status": "空闲"                       // 设备状态 (可选)
}
```

## 支持的任务类型

| 任务类型 | 说明 | 示例场景 |
|---------|------|----------|
| `like` | 点赞操作 | 视频点赞、作品点赞 |
| `collect` | 收藏操作 | 收藏视频、收藏作品 |
| `follow` | 关注操作 | 关注用户、关注账号 |
| `comment` | 评论操作 | 发表评论、回复评论 |
| `message` | 私信操作 | 发送私信、回复私信 |
| `recommend` | 推荐操作 | 推荐视频、推荐用户 |
| `comment_like` | 评论点赞 | 对评论进行点赞 |
| `comment_reply` | 评论回复 | 回复他人评论 |

## 设备状态说明

| 状态 | 说明 |
|------|------|
| `空闲` | 设备空闲，可接受新任务 |
| `忙碌` | 设备正在执行任务 |
| `掉线` | 设备离线 |

## 客户端示例代码

### JavaScript/Node.js 示例

```javascript
const axios = require('axios');

class DeviceReporter {
    constructor(baseUrl, deviceName, userName) {
        this.baseUrl = baseUrl;
        this.deviceName = deviceName;
        this.userName = userName;
    }
    
    // 上报单个任务数据
    async reportTask(taskType, success, executionData = {}) {
        try {
            const response = await axios.post(`${this.baseUrl}/wsRouter/device/report`, {
                device_name: this.deviceName,
                user_name: this.userName,
                task_type: taskType,
                success: success,
                execution_data: executionData,
                timestamp: new Date().toISOString()
            });
            
            console.log('上报成功:', response.data);
            return response.data;
        } catch (error) {
            console.error('上报失败:', error.response?.data || error.message);
            throw error;
        }
    }
    
    // 批量上报任务数据
    async batchReport(reports) {
        try {
            const response = await axios.post(`${this.baseUrl}/wsRouter/device/batchReport`, {
                user_name: this.userName,
                reports: reports
            });
            
            console.log('批量上报成功:', response.data);
            return response.data;
        } catch (error) {
            console.error('批量上报失败:', error.response?.data || error.message);
            throw error;
        }
    }
    
    // 发送心跳
    async sendHeartbeat(status = '空闲') {
        try {
            const response = await axios.post(`${this.baseUrl}/wsRouter/device/heartbeat`, {
                device_name: this.deviceName,
                user_name: this.userName,
                status: status
            });
            
            console.log('心跳发送成功:', response.data);
            return response.data;
        } catch (error) {
            console.error('心跳发送失败:', error.response?.data || error.message);
            throw error;
        }
    }
}

// 使用示例
const reporter = new DeviceReporter('http://43.143.83.131:15001', 'device_001', 'cjroot');

// 上报点赞任务
reporter.reportTask('like', true, {
    target_url: 'https://example.com/video/123',
    duration: 1500
});

// 批量上报
reporter.batchReport([
    {
        device_name: 'device_001',
        task_type: 'like',
        success: true,
        execution_data: { target_url: 'https://example.com/video/123' }
    },
    {
        device_name: 'device_001',
        task_type: 'collect',
        success: true,
        execution_data: { target_url: 'https://example.com/video/456' }
    }
]);

// 定期发送心跳
setInterval(() => {
    reporter.sendHeartbeat('空闲');
}, 30000); // 每30秒发送一次心跳
```

### Python 示例

```python
import requests
import json
import time
from datetime import datetime

class DeviceReporter:
    def __init__(self, base_url, device_name, user_name):
        self.base_url = base_url
        self.device_name = device_name
        self.user_name = user_name
    
    def report_task(self, task_type, success, execution_data=None):
        """上报单个任务数据"""
        url = f"{self.base_url}/wsRouter/device/report"
        data = {
            'device_name': self.device_name,
            'user_name': self.user_name,
            'task_type': task_type,
            'success': success,
            'execution_data': execution_data or {},
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            response = requests.post(url, json=data)
            result = response.json()
            print(f'上报成功: {result}')
            return result
        except Exception as e:
            print(f'上报失败: {e}')
            raise e
    
    def batch_report(self, reports):
        """批量上报任务数据"""
        url = f"{self.base_url}/wsRouter/device/batchReport"
        data = {
            'user_name': self.user_name,
            'reports': reports
        }
        
        try:
            response = requests.post(url, json=data)
            result = response.json()
            print(f'批量上报成功: {result}')
            return result
        except Exception as e:
            print(f'批量上报失败: {e}')
            raise e
    
    def send_heartbeat(self, status='空闲'):
        """发送心跳"""
        url = f"{self.base_url}/wsRouter/device/heartbeat"
        data = {
            'device_name': self.device_name,
            'user_name': self.user_name,
            'status': status
        }
        
        try:
            response = requests.post(url, json=data)
            result = response.json()
            print(f'心跳发送成功: {result}')
            return result
        except Exception as e:
            print(f'心跳发送失败: {e}')
            raise e

# 使用示例
reporter = DeviceReporter('http://43.143.83.131:15001', 'device_001', 'cjroot')

# 上报点赞任务
reporter.report_task('like', True, {
    'target_url': 'https://example.com/video/123',
    'duration': 1500
})

# 批量上报
reports = [
    {
        'device_name': 'device_001',
        'task_type': 'like',
        'success': True,
        'execution_data': {'target_url': 'https://example.com/video/123'}
    },
    {
        'device_name': 'device_001',
        'task_type': 'collect',
        'success': True,
        'execution_data': {'target_url': 'https://example.com/video/456'}
    }
]
reporter.batch_report(reports)

# 定期发送心跳
import threading
def heartbeat_loop():
    while True:
        try:
            reporter.send_heartbeat('空闲')
            time.sleep(30)  # 每30秒发送一次心跳
        except Exception as e:
            print(f'心跳发送异常: {e}')
            time.sleep(5)

# 启动心跳线程
heartbeat_thread = threading.Thread(target=heartbeat_loop, daemon=True)
heartbeat_thread.start()
```

## 重要特性

### 🔐 自动设备认证
- 设备首次上报数据时自动注册
- 自动绑定到指定用户
- 无需手动配置设备信息

### 📊 实时数据统计
- 自动记录任务执行统计
- 支持成功/失败计数
- 实时更新设备状态

### 🚀 高并发支持
- 支持批量数据上报
- 数据库连接池优化
- 事务保证数据一致性

### 💾 双重数据存储
- **MongoDB**: 设备管理和用户绑定
- **MySQL**: 任务统计和日志记录

## 错误处理

### 常见错误码

- `0`: 成功
- `-1`: 失败，查看msg字段获取详细信息

### 常见错误情况

1. **用户不存在**: 确保user_name正确
2. **参数缺失**: 检查必填参数是否完整
3. **网络连接**: 确保服务器地址和端口正确
4. **设备重复**: 设备名称在同一用户下必须唯一

## 监控和调试

### 查看设备状态
访问监控页面: `http://43.143.83.131:15001/page/task/douyin514phoneNum.html`

### 日志查看
- 服务器端会记录详细的操作日志
- 客户端建议添加本地日志记录

## 最佳实践

1. **定期心跳**: 建议每30秒发送一次心跳
2. **批量上报**: 对于高频任务，建议使用批量接口
3. **错误重试**: 网络异常时实现重试机制
4. **数据缓存**: 网络不稳定时可本地缓存数据
5. **状态同步**: 及时更新设备状态信息
