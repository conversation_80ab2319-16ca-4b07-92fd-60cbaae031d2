<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui优化版</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
    <style>
        .image-preview {
            margin-top: 10px;
            display: flex;
            gap: 10px;
        }

        .preview-item {
            position: relative;
            width: 100px;
            height: 100px;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
        }

        .preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .delete-btn {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            background: #ff4444;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 20px;
            cursor: pointer;
        }

        /* 模式切换按钮样式 */
        .mode-switch-buttons {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f8f8;
            border-radius: 5px;
            border: 1px solid #e6e6e6;
        }

        .mode-switch-buttons .layui-btn {
            margin: 0 5px;
            min-width: 100px;
        }

        .mode-switch-buttons .layui-btn-disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .current-mode-display {
            margin-top: 8px;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <form class="layui-form layui-form-pane" action="">
                <!-- 隐藏字段存储当前模式 -->
                <input type="hidden" id="currentMode" value="likeList">

                <fieldset style="border-color: blue;border-width: 3px; background-color: rgb(255, 252, 194);">
                    <legend style="color: blue;font-weight: bold;font-size: 16px;">注意：请选择分组(点击全选,双击展开组内设备,可单选可多选) <input id="inputaaa" />
                    </legend>
                    <input type="checkbox" lay-filter="quanXuan1" title="全选" />
                    <br>
                    <br>
                    <div class="layui-form-item" id="groups1"></div>
                </fieldset>
                
                <fieldset>
                    <legend>【点赞收藏列表】功能</legend>

                    <div class="layui-form-item">
                        <label class="layui-form-label">模式选择</label>
                        <div class="layui-input-block">
                            <!-- 模式切换按钮 -->
                            <div class="mode-switch-buttons">
                                <button type="button" class="layui-btn layui-btn-normal layui-btn-sm" id="likeListBtn" onclick="switchMode('likeList')">
                                    <i class="layui-icon layui-icon-praise"></i> 点赞列表
                                </button>
                                <button type="button" class="layui-btn layui-btn-warm layui-btn-sm layui-btn-disabled" id="collectListBtn" onclick="switchMode('collectList')">
                                    <i class="layui-icon layui-icon-star-fill"></i> 收藏列表
                                </button>
                                <div class="current-mode-display">
                                    <span id="currentModeText" style="font-weight: bold; color: #1E9FFF;">当前模式：点赞列表</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">功能选项</label>
                        <div class="layui-input-block">
                            <!-- 评论区操作 -->
                            <div style="margin: 15px 0 10px 0;font-weight: bold;">评论区操作</div>
                            <input type="checkbox" name="commentLikeSwitch" lay-skin="switch" lay-text="评论区点赞|评论区点赞" onchange="document.getElementById('commentLikeSwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="commentLikeSwitchValue" name="commentLikeSwitchValue" value="off">

                            <input type="checkbox" name="commentReplySwitch" lay-skin="switch" lay-text="评论区回复|评论区回复" onchange="document.getElementById('commentReplySwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="commentReplySwitchValue" name="commentReplySwitchValue" value="off">

                            <!-- 基础功能 -->
                            <div style="margin: 15px 0 10px 0;font-weight: bold;">视频操作</div>
                            <input type="checkbox" name="likeSwitch" lay-skin="switch" lay-text="点赞|点赞" onchange="document.getElementById('likeSwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="likeSwitchValue" name="likeSwitchValue" value="off">
                            
                            <input type="checkbox" name="commentSwitch" lay-skin="switch" lay-text="评论|评论" onchange="document.getElementById('commentSwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="commentSwitchValue" name="commentSwitchValue" value="off">
                            
                            <input type="checkbox" name="collectSwitch" lay-skin="switch" lay-text="收藏|收藏" onchange="document.getElementById('collectSwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="collectSwitchValue" name="collectSwitchValue" value="off">
                            
                            <input type="checkbox" name="followSwitch" lay-skin="switch" lay-text="关注|关注" onchange="document.getElementById('followSwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="followSwitchValue" name="followSwitchValue" value="off">

                            <input type="checkbox" name="homeMessageSwitch" lay-skin="switch" lay-text="作者私信|作者私信" onchange="document.getElementById('homeMessageSwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="homeMessageSwitchValue" name="homeMessageSwitchValue" value="off">
                            
                            <input type="checkbox" name="recommendSwitch" lay-skin="switch" lay-text="视频推荐|视频推荐" onchange="document.getElementById('recommendSwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="recommendSwitchValue" name="recommendSwitchValue" value="off">
                            
                            <!-- 用户互动功能 -->
                            <div style="margin: 15px 0 10px 0;font-weight: bold;">评论区用户操作</div>
                            <input type="checkbox" name="commentUserFollowSwitch" lay-skin="switch" lay-text="评论区用户关注|评论区用户关注" onchange="document.getElementById('commentUserFollowSwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="commentUserFollowSwitchValue" name="commentUserFollowSwitchValue" value="off">
                            
                            <input type="checkbox" name="commentUserLikeSwitch" lay-skin="switch" lay-text="评论区用户点赞|评论区用户点赞" onchange="document.getElementById('commentUserLikeSwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="commentUserLikeSwitchValue" name="commentUserLikeSwitchValue" value="off">
                            
                            <input type="checkbox" name="commentUserCollectSwitch" lay-skin="switch" lay-text="评论区用户收藏|评论区用户收藏" onchange="document.getElementById('commentUserCollectSwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="commentUserCollectSwitchValue" name="commentUserCollectSwitchValue" value="off">
                            
                            <input type="checkbox" name="commentUserMessageSwitch" lay-skin="switch" lay-text="评论区用户收藏|评论区用户私信" onchange="document.getElementById('commentUserMessageSwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="commentUserMessageSwitchValue" name="commentUserMessageSwitchValue" value="off">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-block">
                            <textarea id="keyword" name="keyword" placeholder="多个关键词请用换行分隔" class="layui-textarea"></textarea>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">评论内容/@id</label>
                        <div class="layui-input-block">
                            <textarea id="commentContent" name="commentContent" placeholder="请输入评论内容/@id" class="layui-textarea"></textarea>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">链接</label>
                        <div class="layui-input-block">
                            <textarea  id="linkUrl" name="linkUrl" placeholder="请输入链接地址，多个链接请用换行分隔" class="layui-input"></textarea>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">上传图片</label>
                        <div class="layui-input-block">
                            <input type="file" id="imageUpload" accept="image/*" multiple>
                            <div class="image-preview" id="imagePreview"></div>
                            <!-- 新增隐藏字段存储图片数据 -->
                            <textarea id="imageData" name="imageData" style="display:none;"></textarea>
                        </div>
                    </div>

                    <button class="layui-btn layui-btn-sm" id="executeTaskBtn" value="点赞收藏列表任务" lay-submit="" lay-filter="tijiao">执行任务</button>
                    <button class="layui-btn layui-btn-sm" lay-submit="" lay-filter="stopTask">停止任务</button>
                </fieldset>
            </form>
        </div>
    </div>

    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script src="/js/lay-config.js?v=2.0.4" charset="utf-8"></script>
    <script>
        layui.use('tongYong', function () {
            var tongYong = layui.tongYong;
            tongYong.tongYong1();
        });

        // 模式切换功能
        function switchMode(mode) {
            const currentModeInput = document.getElementById('currentMode');
            const currentModeText = document.getElementById('currentModeText');
            const likeListBtn = document.getElementById('likeListBtn');
            const collectListBtn = document.getElementById('collectListBtn');
            const executeTaskBtn = document.getElementById('executeTaskBtn');

            // 更新当前模式
            currentModeInput.value = mode;

            // 更新按钮样式和文本
            if (mode === 'likeList') {
                likeListBtn.className = 'layui-btn layui-btn-normal layui-btn-sm';
                collectListBtn.className = 'layui-btn layui-btn-warm layui-btn-sm layui-btn-disabled';
                currentModeText.textContent = '当前模式：点赞列表';
                currentModeText.style.color = '#1E9FFF';

                // 更新执行任务按钮
                executeTaskBtn.value = '点赞列表任务';
                executeTaskBtn.innerHTML = '<i class="layui-icon layui-icon-praise"></i> 执行点赞列表任务';
            } else {
                likeListBtn.className = 'layui-btn layui-btn-normal layui-btn-sm layui-btn-disabled';
                collectListBtn.className = 'layui-btn layui-btn-warm layui-btn-sm';
                currentModeText.textContent = '当前模式：收藏列表';
                currentModeText.style.color = '#FFB800';

                // 更新执行任务按钮
                executeTaskBtn.value = '收藏列表任务';
                executeTaskBtn.innerHTML = '<i class="layui-icon layui-icon-star-fill"></i> 执行收藏列表任务';
            }

            // 输出模式切换信息
            console.log('模式已切换到:', mode, mode === 'likeList' ? '点赞列表' : '收藏列表');

            // 可以在这里添加加载对应数据的逻辑
            loadModeData(mode);
        }

        // 加载对应模式的数据
        function loadModeData(mode) {
            const userName = localStorage.getItem("userName");
            if (!userName) {
                layui.layer.msg('请先登录');
                return;
            }

            // 根据模式生成对应的任务ID
            const taskId = generateTaskId(mode);

            // 这里可以调用API获取对应的数据
            console.log(`加载${mode}数据，任务ID: ${taskId}`);

            // 示例：可以调用后端API获取数据
            // axios.get(`/api/getTaskData/${taskId}`)
            //     .then(response => {
            //         // 处理返回的数据
            //         console.log('获取到数据:', response.data);
            //     })
            //     .catch(error => {
            //         console.error('获取数据失败:', error);
            //     });
        }

        // 生成任务ID的函数
        function generateTaskId(mode) {
            const userName = localStorage.getItem("userName") || "default";
            const timestamp = Date.now();
            const modePrefix = mode === 'likeList' ? 'LIKE' : 'COLLECT';
            return `${modePrefix}_${userName}_${timestamp}`;
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认选择点赞列表模式
            switchMode('likeList');
        });

        // 图片上传处理
        const imageUpload = document.getElementById('imageUpload');
        const imagePreview = document.getElementById('imagePreview');
        const imageDataField = document.getElementById('imageData');
        let imageList = []; // 存储图片信息

        imageUpload.addEventListener('change', async function (e) {
            const files = e.target.files;
            if (!files || files.length === 0) return;

            imagePreview.innerHTML = '上传中...';
            imageList = [];

            const formData = new FormData();
            // 添加所有文件
            for (let i = 0; i < files.length; i++) {
                formData.append('files', files[i]);
            }

            try {
                const response = await axios.post('/localUpload', formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                });

                if (response.data && response.data.paths) {
                    const baseUrl = response.data.baseUrl || `http://${window.location.host}`;
                    const fileUrls = response.data.paths.map(path => 
                        path.startsWith('http') ? path : `${baseUrl}${path}`
                    );
                    imageList = fileUrls;
                    
                    // 显示图片预览
                    imagePreview.innerHTML = '';
                    fileUrls.forEach(url => {
                        const div = document.createElement('div');
                        div.className = 'preview-item';
                        div.innerHTML = `
                            <img src="${url}" alt="预览图">
                            <span class="delete-btn" data-url="${url}">×</span>
                        `;
                        imagePreview.appendChild(div);
                    });
                    
                    // 更新隐藏字段，只包含URL
                    imageDataField.value = fileUrls.join(',');
                }
            } catch (error) {
                console.error('上传失败:', error);
                imagePreview.innerHTML = '上传失败';
                layer.msg('图片上传失败: ' + error.message, {icon: 2});
            }
        });

        // 点击删除预览图
        imagePreview.addEventListener('click', function (e) {
            if (e.target.classList.contains('delete-btn')) {
                const imageUrl = e.target.dataset.url;
                // 从数组中移除对应图片
                imageList = imageList.filter(url => url !== imageUrl);
                e.target.parentElement.remove();
                
                // 更新隐藏字段
                updateImageDataField();
            }
        });

        // 更新隐藏字段内容
        function updateImageDataField() {
        // 更新隐藏字段，只包含URL
        imageDataField.value = imageList.join(',');
        }

        // 移除原生的点击事件监听器，让layui的表单提交系统正常工作
        // 这样tongYong.js中的btn_renWu函数就能正确获取到最新的表单数据

        // 但是需要在layui表单提交前设置正确的任务类型
        layui.use(['form'], function() {
            var form = layui.form;

            // 监听表单提交，在提交前设置正确的任务类型
            form.on('submit(tijiao)', function(data) {
                // 获取当前模式
                const currentMode = document.getElementById('currentMode').value;

                // 更新按钮的value属性，确保传递正确的任务类型
                data.elem.value = currentMode === 'likeList' ? '点赞列表任务' : '收藏列表任务';

                // 让tongYong的默认处理继续
                return false;
            });
        });





        // 停止任务按钮
        document.querySelector(".layui-btn[lay-filter='stopTask']").addEventListener("click", function (event) {
            event.preventDefault();
            layui.layer.confirm('确定要停止当前任务吗？', { icon: 3, title: '提示' }, function (index) {
                axios.post('/wsRouter/faTast', {
                    data: {
                        taskName: "停止任务",
                        userName: localStorage.getItem("userName") || "",
                        timeStamp: Date.now()
                    }
                }).then(response => {
                    if (response.data.code === 1) {
                        layui.layer.msg('任务已停止', { icon: 1 });
                    } else {
                        layui.layer.msg('停止任务失败: ' + response.data.msg, { icon: 2 });
                    }
                }).catch(error => {
                    layui.layer.msg('停止任务失败: ' + error.message, { icon: 2 });
                }).finally(() => {
                    layer.close(index);
                });
            });
        });
    </script>
</body>

</html>