const express = require('express');
const router = express.Router();
const mongoose = require('../db/db.js');
const messageShe = require('../db/messageShe');
const linkDataShe = require('../db/linkDataShe');
const axios = require('axios');
const { pool } = require('../db/mysql.js');

// 创建用户
router.post('/createUser', async (req, res) => {
    const { username, password } = req.body;
    
    if (!username || !password) {
        return res.status(400).json({ 
            code: -1,
            msg: '用户名和密码不能为空' 
        });
    }

    try {
        const [result] = await pool.query(
            'INSERT INTO users (username, password) VALUES (?, ?)',
            [username, password]
        );
        
        res.json({ 
            code: 0,
            msg: '用户创建成功',
            data: { userId: result.insertId }
        });
    } catch (err) {
        if (err.code === 'ER_DUP_ENTRY') {
            res.status(400).json({ 
                code: -1,
                msg: '用户名已存在' 
            });
        } else {
            console.error('创建用户失败:', err);
            res.status(500).json({ 
                code: -1,
                msg: '用户创建失败' 
            });
        }
    }
});

// 验证用户
router.post('/verifyUser', async (req, res) => {
    const { username, password } = req.body;
    
    if (!username || !password) {
        return res.status(400).json({ 
            code: -1,
            msg: '用户名和密码不能为空' 
        });
    }

    try {
        const [rows] = await pool.query(
            'SELECT id FROM users WHERE username = ? AND password = ?',
            [username, password]
        );
        
        if (rows.length > 0) {
            res.json({ 
                code: 0,
                msg: '验证成功',
                data: { valid: true }
            });
        } else {
            res.json({ 
                code: 0,
                msg: '用户名或密码错误',
                data: { valid: false }
            });
        }
    } catch (err) {
        console.error('验证用户失败:', err);
        res.status(500).json({ 
            code: -1,
            msg: '验证失败' 
        });
    }
});

// 设备日志记录接口
router.post('/device/log', async (req, res) => {
    try {
        const { device_id, status, execution_data } = req.body;
        
        if (!device_id || !status || !execution_data) {
            return res.status(400).json({
                code: -1,
                msg: '缺少必要参数'
            });
        }

        const DeviceLog = mongoose.model('device_logs', messageShe);
        const newLog = new DeviceLog({
            device_id,
            status,
            execution_data
        });
        
        await newLog.save();
        
        res.json({
            code: 0,
            msg: '日志记录成功'
        });
    } catch (error) {
        console.error('记录设备日志失败:', error);
        res.status(500).json({
            code: -1,
            msg: '日志记录失败: ' + error.message
        });
    }
});

// 保存链接数据
router.post('/saveLinkData', async (req, res) => {
    try {
        const linkDataMod = mongoose.model('linkData', linkDataShe);
        const newLinkData = new linkDataMod(req.body);
        await newLinkData.save();
        res.json({code: 1, msg: '保存成功'});
    } catch (error) {
        console.error('保存链接数据错误:', error);
        res.json({code: -1, msg: '保存失败'});
    }
});

/**
 * @api {get} /api/getLinkData 获取链接数据
 * @apiName GetLinkData
 * @apiGroup Link
 * 
 * @apiDescription 根据用户名查询对应的链接数据，按创建时间倒序排列
 * 
 * @apiParam {String} userName 用户名(必需)
 * @apiParam {Number} [_] 时间戳(可选，用于避免缓存)
 * 
 * @apiSuccess {Number} code 状态码(1=成功)
 * @apiSuccess {String} msg 返回消息
 * @apiSuccess {Object[]} data 链接数据数组
 * @apiSuccess {String} data.phoneNumber 设备号码
 * @apiSuccess {String} data.username 用户名
 * @apiSuccess {String} data.links 链接内容
 * 
 * @apiError {Number} code 状态码(-1=失败)
 * @apiError {String} msg 错误信息
 * @apiError {Null} data 空数据
 * 
 * @apiExample 使用示例:
 * GET /api/getLinkData?userName=test123&_=1620000000000
 */
router.get('/getLinkData', async (req, res) => {
    try {
        const userName = req.query.userName || req.body.userName;
        if (!userName) {
            return res.status(400).json({ 
                code: -1,
                msg: '缺少userName参数',
                data: null 
            });
        }

        console.log('查询链接数据，userName:', userName);
        const LinkData = mongoose.model('linkData_' + userName, linkDataShe);
        const data = await LinkData.find({})
            .sort({ createdAt: -1 })
            .lean();
        
        console.log('查询到数据条数:', data.length);
        
        res.json({
            code: 1,
            msg: '获取成功',
            data: data
        });
    } catch (error) {
        console.error('[ERROR] 获取链接数据失败:', error);
        res.status(500).json({ 
            code: -1,
            msg: '获取链接数据失败: ' + error.message,
            data: null
        });
    }
});

/**
 * @api {get} /api/device/logs 获取设备日志
 * @apiName GetDeviceLogs
 * @apiGroup Device
 * 
 * @apiParam {String} [device_id] 设备ID(可选)
 * @apiParam {Number} [limit] 返回条数限制(可选)
 * 
 * @apiSuccess {Number} code 状态码(1=成功)
 * @apiSuccess {Object[]} data 日志数据
 * @apiSuccess {String} data.device_id 设备ID
 * @apiSuccess {String} data.status 状态(running/completed/failed/pending)
 * @apiSuccess {String} data.execution_data 执行内容
 * @apiSuccess {Date} data.created_at 创建时间
 */
// 兼容15001端口的路由
// 查询缓存
const queryCache = new Map();
const CACHE_TTL = 30000; // 30秒缓存

/**
 * @api {get} /api/device/logs 查询设备日志
 * @apiName GetDeviceLogs
 * @apiGroup Device
 *
 * @apiParam {String} [device_id] 设备ID筛选
 * @apiParam {String="pending","running","completed","failed"} [status] 状态筛选
 * @apiParam {String} [start_time] 开始时间(ISO格式)
 * @apiParam {String} [end_time] 结束时间(ISO格式)
 * @apiParam {Number} [page=1] 页码
 * @apiParam {Number} [limit=20] 每页条数
 */
router.get('/device/logs', async (req, res) => {
    try {
        // 参数处理
        let { device_id, status, start_time, end_time, page = 1, limit = 20 } = req.query;
        page = Math.max(1, parseInt(page));
        limit = Math.min(100, Math.max(1, parseInt(limit)));
        
        console.log('设备日志查询参数:', req.query);
        const cacheKey = `logs_${device_id || 'all'}_${status || 'all'}_${start_time || ''}_${end_time || ''}_${page}_${limit}`;
        
        // 检查缓存
        if (queryCache.has(cacheKey)) {
            const { timestamp, data } = queryCache.get(cacheKey);
            if (Date.now() - timestamp < CACHE_TTL) {
                return res.json(data);
            }
        }

        const DeviceLog = mongoose.model('device_logs', require('../db/messageShe'));
        const query = {};
        
        // 构建查询条件
        if (device_id) query.device_id = device_id;
        if (status) query.status = status;
        if (start_time || end_time) {
            query.created_at = {};
            if (start_time) query.created_at.$gte = new Date(start_time);
            if (end_time) query.created_at.$lte = new Date(end_time);
        }
        
        // 使用连接池获取连接
        const [logs, total] = await Promise.all([
            DeviceLog.find(query)
                .select('device_id status execution_data created_at')
                .sort({ created_at: -1 })
                .skip((page - 1) * limit)
                .limit(parseInt(limit))
                .lean()
                .exec(),
            DeviceLog.countDocuments(query).exec()
        ]);

        // 设置缓存
        const responseData = {
            code: 0,
            data: {
                list: logs,
                total,
                page: parseInt(page),
                limit: parseInt(limit)
            }
        };
        queryCache.set(cacheKey, {
            timestamp: Date.now(),
            data: responseData
        });
        
        res.json(responseData);
        
    } catch (error) {
        console.error('查询设备日志失败:', error);
        res.status(500).json({
            code: -1,
            msg: '查询日志失败: ' + error.message
        });
    }
});

module.exports = router;
