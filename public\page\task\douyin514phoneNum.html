<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>设备任务监控</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
    <style>
        .task-stats-card {
            margin-bottom: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 4px solid #1E9FFF;
        }

        .stat-item.like { border-left-color: #FF5722; }
        .stat-item.collect { border-left-color: #FF9800; }
        .stat-item.follow { border-left-color: #4CAF50; }
        .stat-item.comment { border-left-color: #9C27B0; }
        .stat-item.message { border-left-color: #2196F3; }
        .stat-item.recommend { border-left-color: #607D8B; }
        .stat-item.comment-like { border-left-color: #E91E63; }
        .stat-item.comment-reply { border-left-color: #795548; }
        /* 新增的评论区操作样式 */
        .stat-item.comment-area-like { border-left-color: #FF6B6B; }
        .stat-item.comment-area-reply { border-left-color: #4ECDC4; }
        .stat-item.comment-user-like { border-left-color: #45B7D1; }
        .stat-item.comment-user-recommend { border-left-color: #96CEB4; }
        .stat-item.comment-user-collect { border-left-color: #FFEAA7; }
        .stat-item.comment-user-message { border-left-color: #DDA0DD; }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
        }

        .device-filter {
            margin-bottom: 15px;
        }

        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 999;
        }

        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            color: white;
        }

        .status-success { background-color: #5FB878; }
        .status-warning { background-color: #FFB800; }
        .status-danger { background-color: #FF5722; }
        .status-info { background-color: #01AAED; }
    </style>
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main">

            <!-- 任务统计卡片 -->
            <div class="layui-card task-stats-card">
                <div class="layui-card-header">
                    <i class="layui-icon layui-icon-chart"></i> 任务执行统计
                    <div class="layui-btn-group" style="float: right;">
                        <button class="layui-btn layui-btn-sm" id="refreshStats">
                            <i class="layui-icon layui-icon-refresh"></i> 刷新统计
                        </button>
                    </div>
                </div>
                <div class="layui-card-body">
                    <div class="stats-grid">
                        <div class="stat-item like">
                            <div class="stat-number" id="likeCount">0</div>
                            <div class="stat-label">点赞</div>
                        </div>
                        <div class="stat-item collect">
                            <div class="stat-number" id="collectCount">0</div>
                            <div class="stat-label">收藏</div>
                        </div>
                        <div class="stat-item follow">
                            <div class="stat-number" id="followCount">0</div>
                            <div class="stat-label">关注</div>
                        </div>
                        <div class="stat-item comment">
                            <div class="stat-number" id="commentCount">0</div>
                            <div class="stat-label">评论</div>
                        </div>
                        <div class="stat-item message">
                            <div class="stat-number" id="messageCount">0</div>
                            <div class="stat-label">私信</div>
                        </div>
                        <div class="stat-item recommend">
                            <div class="stat-number" id="recommendCount">0</div>
                            <div class="stat-label">推荐</div>
                        </div>
                        <div class="stat-item comment-like">
                            <div class="stat-number" id="commentLikeCount">0</div>
                            <div class="stat-label">评论点赞</div>
                        </div>
                        <div class="stat-item comment-reply">
                            <div class="stat-number" id="commentReplyCount">0</div>
                            <div class="stat-label">评论回复</div>
                        </div>
                        <!-- 新增的评论区操作统计 -->
                        <div class="stat-item comment-area-like">
                            <div class="stat-number" id="commentLikeNewCount">0</div>
                            <div class="stat-label">评论区点赞</div>
                        </div>
                        <div class="stat-item comment-area-reply">
                            <div class="stat-number" id="commentReplyNewCount">0</div>
                            <div class="stat-label">评论区回复</div>
                        </div>
                        <div class="stat-item comment-user-like">
                            <div class="stat-number" id="commentUserLikeCount">0</div>
                            <div class="stat-label">评论区用户点赞</div>
                        </div>
                        <div class="stat-item comment-user-recommend">
                            <div class="stat-number" id="commentUserRecommendCount">0</div>
                            <div class="stat-label">评论区用户推荐</div>
                        </div>
                        <div class="stat-item comment-user-collect">
                            <div class="stat-number" id="commentUserCollectCount">0</div>
                            <div class="stat-label">评论区用户收藏</div>
                        </div>
                        <div class="stat-item comment-user-message">
                            <div class="stat-number" id="commentUserMessageCount">0</div>
                            <div class="stat-label">评论区用户私信</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 设备任务详情表格 -->
            <div class="layui-card">
                <div class="layui-card-header">
                    <i class="layui-icon layui-icon-list"></i> 设备任务详情
                    <div class="device-filter" style="float: right;">
                        <select id="deviceFilter" lay-filter="deviceFilter">
                            <option value="">全部设备</option>
                        </select>
                        <select id="statusFilter" lay-filter="statusFilter">
                            <option value="">全部状态</option>
                            <option value="completed">已完成</option>
                            <option value="running">运行中</option>
                            <option value="failed">失败</option>
                            <option value="pending">待处理</option>
                        </select>
                    </div>
                </div>
                <div class="layui-card-body">
                    <table id="deviceTaskTable" lay-filter="deviceTaskTable"></table>
                </div>
            </div>
        </div>
    </div>

    <!-- 悬浮刷新按钮 -->
    <button class="layui-btn layui-btn-primary layui-btn-radius refresh-btn" id="autoRefreshBtn">
        <i class="layui-icon layui-icon-refresh-3"></i> 自动刷新: 开启
    </button>

    <script src="/lib/jquery-3.4.1/jquery-3.4.1.min.js"></script>
    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script src="/js/lay-config.js?v=2.0.4" charset="utf-8"></script>

    <script>
        layui.use(['jquery', 'table', 'layer', 'util', 'form'], function(){
            var $ = layui.$;
            var table = layui.table;
            var layer = layui.layer;
            var util = layui.util;
            var form = layui.form;

            // 全局变量
            let autoRefreshTimer = null;
            let isAutoRefresh = true;
            let selectedDevices = [];
            
            // 初始化设备任务表格
            var taskTable = table.render({
                elem: '#deviceTaskTable',
                url: '/wsRouter/getDeviceTaskStats',
                toolbar: '#deviceToolbar',
                page: true,
                limit: 20,
                limits: [10, 20, 50, 100],
                parseData: function(res) {
                    console.log('表格数据响应:', res); // 调试日志
                    return {
                        "code": res.code === 0 ? 0 : 1,
                        "msg": res.msg || '',
                        "count": res.count || 0,
                        "data": res.data || []
                    };
                },
                cols: [[
                    {field: 'device_id', title: '设备ID', width: 150, fixed: 'left'},
                    {field: 'device_name', title: '设备名称', width: 120},
                    {field: 'like_count', title: '点赞', width: 80, templet: function(d){
                        return '<span class="status-badge status-danger">' + (d.like_count || 0) + '</span>';
                    }},
                    {field: 'collect_count', title: '收藏', width: 80, templet: function(d){
                        return '<span class="status-badge status-warning">' + (d.collect_count || 0) + '</span>';
                    }},
                    {field: 'follow_count', title: '关注', width: 80, templet: function(d){
                        return '<span class="status-badge status-success">' + (d.follow_count || 0) + '</span>';
                    }},
                    {field: 'comment_count', title: '评论', width: 80, templet: function(d){
                        return '<span class="status-badge status-info">' + (d.comment_count || 0) + '</span>';
                    }},
                    {field: 'message_count', title: '私信', width: 80, templet: function(d){
                        return '<span class="status-badge status-info">' + (d.message_count || 0) + '</span>';
                    }},
                    {field: 'recommend_count', title: '推荐', width: 80, templet: function(d){
                        return '<span class="status-badge status-info">' + (d.recommend_count || 0) + '</span>';
                    }},
                    {field: 'comment_like_count', title: '评论点赞', width: 100, templet: function(d){
                        return '<span class="status-badge status-danger">' + (d.comment_like_count || 0) + '</span>';
                    }},
                    {field: 'comment_reply_count', title: '评论回复', width: 100, templet: function(d){
                        return '<span class="status-badge status-info">' + (d.comment_reply_count || 0) + '</span>';
                    }},
                    // 新增的评论区操作列
                    {field: 'commentLike_count', title: '评论区点赞', width: 110, templet: function(d){
                        return '<span class="status-badge status-danger">' + (d.commentLike_count || 0) + '</span>';
                    }},
                    {field: 'commentReply_count', title: '评论区回复', width: 110, templet: function(d){
                        return '<span class="status-badge status-info">' + (d.commentReply_count || 0) + '</span>';
                    }},
                    {field: 'commentUserLike_count', title: '评论区用户点赞', width: 130, templet: function(d){
                        return '<span class="status-badge status-success">' + (d.commentUserLike_count || 0) + '</span>';
                    }},
                    {field: 'commentUserRecommend_count', title: '评论区用户推荐', width: 130, templet: function(d){
                        return '<span class="status-badge status-warning">' + (d.commentUserRecommend_count || 0) + '</span>';
                    }},
                    {field: 'commentUserCollect_count', title: '评论区用户收藏', width: 130, templet: function(d){
                        return '<span class="status-badge status-warning">' + (d.commentUserCollect_count || 0) + '</span>';
                    }},
                    {field: 'commentUserMessage_count', title: '评论区用户私信', width: 130, templet: function(d){
                        return '<span class="status-badge status-info">' + (d.commentUserMessage_count || 0) + '</span>';
                    }},
                    {field: 'last_active', title: '最后活动', width: 160, templet: function(d){
                        return d.last_active ? util.toDateString(d.last_active, 'yyyy-MM-dd HH:mm:ss') : '无记录';
                    }},
                    {field: 'status', title: '状态', width: 100, templet: function(d){
                        var statusMap = {
                            'online': {text: '在线', class: 'status-success'},
                            'offline': {text: '离线', class: 'status-danger'},
                            'busy': {text: '忙碌', class: 'status-warning'}
                        };
                        var statusInfo = statusMap[d.status] || {text: '未知', class: 'status-info'};
                        return '<span class="status-badge ' + statusInfo.class + '">' + statusInfo.text + '</span>';
                    }},
                    {fixed: 'right', title: '操作', width: 150, align:'center', toolbar: '#deviceActionBar'}
                ]],
                done: function(res, curr, count){
                    // 更新统计数据
                    updateTaskStats(res.data);
                    // 更新设备筛选器
                    updateDeviceFilter(res.data);
                }
            });

            // 更新任务统计数据
            function updateTaskStats(data) {
                if (!data || !Array.isArray(data)) return;

                let stats = {
                    like: 0, collect: 0, follow: 0, comment: 0,
                    message: 0, recommend: 0, commentLike: 0, commentReply: 0,
                    // 新增的评论区操作统计
                    commentLikeNew: 0, commentReplyNew: 0, commentUserLike: 0,
                    commentUserRecommend: 0, commentUserCollect: 0, commentUserMessage: 0
                };

                data.forEach(function(item) {
                    stats.like += parseInt(item.like_count || 0);
                    stats.collect += parseInt(item.collect_count || 0);
                    stats.follow += parseInt(item.follow_count || 0);
                    stats.comment += parseInt(item.comment_count || 0);
                    stats.message += parseInt(item.message_count || 0);
                    stats.recommend += parseInt(item.recommend_count || 0);
                    stats.commentLike += parseInt(item.comment_like_count || 0);
                    stats.commentReply += parseInt(item.comment_reply_count || 0);
                    // 新增的评论区操作统计
                    stats.commentLikeNew += parseInt(item.commentLike_count || 0);
                    stats.commentReplyNew += parseInt(item.commentReply_count || 0);
                    stats.commentUserLike += parseInt(item.commentUserLike_count || 0);
                    stats.commentUserRecommend += parseInt(item.commentUserRecommend_count || 0);
                    stats.commentUserCollect += parseInt(item.commentUserCollect_count || 0);
                    stats.commentUserMessage += parseInt(item.commentUserMessage_count || 0);
                });

                // 更新统计显示
                $('#likeCount').text(stats.like);
                $('#collectCount').text(stats.collect);
                $('#followCount').text(stats.follow);
                $('#commentCount').text(stats.comment);
                $('#messageCount').text(stats.message);
                $('#recommendCount').text(stats.recommend);
                $('#commentLikeCount').text(stats.commentLike);
                $('#commentReplyCount').text(stats.commentReply);
                // 更新新增的评论区操作统计显示
                $('#commentLikeNewCount').text(stats.commentLikeNew);
                $('#commentReplyNewCount').text(stats.commentReplyNew);
                $('#commentUserLikeCount').text(stats.commentUserLike);
                $('#commentUserRecommendCount').text(stats.commentUserRecommend);
                $('#commentUserCollectCount').text(stats.commentUserCollect);
                $('#commentUserMessageCount').text(stats.commentUserMessage);
            }

            // 更新设备筛选器
            function updateDeviceFilter(data) {
                var deviceFilter = $('#deviceFilter');
                deviceFilter.empty().append('<option value="">全部设备</option>');

                if (data && Array.isArray(data)) {
                    data.forEach(function(item) {
                        deviceFilter.append('<option value="' + item.device_id + '">' +
                                          (item.device_name || item.device_id) + '</option>');
                    });
                }
                form.render('select');
            }

            // 自动刷新功能
            function startAutoRefresh() {
                if (autoRefreshTimer) clearInterval(autoRefreshTimer);

                autoRefreshTimer = setInterval(function() {
                    if (isAutoRefresh) {
                        // 添加时间戳防止缓存
                        const timestamp = new Date().getTime();

                        // 刷新表格数据
                        table.reload('deviceTaskTable', {
                            where: Object.assign(getFilterParams(), { _t: timestamp })
                        });

                        // 刷新统计数据
                        updateStatistics();
                    }
                }, 5000); // 5秒刷新一次，提高实时性
            }

            // 更新统计数据
            function updateStatistics() {
                // 添加时间戳防止缓存
                const timestamp = new Date().getTime();
                axios.get('/wsRouter/getDeviceTaskStats?summary=true&_t=' + timestamp)
                    .then(function(response) {
                        console.log('统计数据响应:', response.data); // 调试日志
                        if(response.data.code === 0 && response.data.summary) {
                            const summary = response.data.summary;

                            // 更新各项统计数字
                            $('#likeCount').text(summary.like_count || 0);
                            $('#collectCount').text(summary.collect_count || 0);
                            $('#followCount').text(summary.follow_count || 0);
                            $('#commentCount').text(summary.comment_count || 0);
                            $('#messageCount').text(summary.message_count || 0);
                            $('#recommendCount').text(summary.recommend_count || 0);
                            $('#commentLikeCount').text(summary.comment_like_count || 0);
                            $('#commentReplyCount').text(summary.comment_reply_count || 0);
                            // 更新新增的评论区操作统计
                            $('#commentLikeNewCount').text(summary.commentLike_count || 0);
                            $('#commentReplyNewCount').text(summary.commentReply_count || 0);
                            $('#commentUserLikeCount').text(summary.commentUserLike_count || 0);
                            $('#commentUserRecommendCount').text(summary.commentUserRecommend_count || 0);
                            $('#commentUserCollectCount').text(summary.commentUserCollect_count || 0);
                            $('#commentUserMessageCount').text(summary.commentUserMessage_count || 0);
                        } else {
                            console.warn('统计数据格式异常:', response.data);
                        }
                    })
                    .catch(function(error) {
                        console.error('更新统计数据失败:', error);
                    });
            }

            // 获取筛选参数
            function getFilterParams() {
                return {
                    device_id: $('#deviceFilter').val(),
                    status: $('#statusFilter').val()
                };
            }
            
            // 设备操作按钮事件
            table.on('tool(deviceTaskTable)', function(obj){
                var data = obj.data;
                if(obj.event === 'detail'){
                    // 查看设备详情
                    showDeviceDetail(data);
                } else if(obj.event === 'reset'){
                    // 重置设备统计
                    resetDeviceStats(data.device_id);
                }
            });

            // 筛选器变化事件
            form.on('select(deviceFilter)', function(data){
                table.reload('deviceTaskTable', {
                    where: getFilterParams()
                });
            });

            form.on('select(statusFilter)', function(data){
                table.reload('deviceTaskTable', {
                    where: getFilterParams()
                });
            });

            // 监听工具栏事件
            table.on('toolbar(deviceTaskTable)', function(obj) {
                switch(obj.event) {
                    case 'generateDemo':
                        generateDemoData();
                        break;
                    case 'clearAll':
                        clearAllData();
                        break;
                    case 'batchDelete':
                        batchDeleteDevices();
                        break;
                }
            });

            // 监听行工具事件
            table.on('tool(deviceTaskTable)', function(obj) {
                var data = obj.data;
                switch(obj.event) {
                    case 'detail':
                        showDeviceDetail(data);
                        break;
                    case 'del':
                        deleteDevice(data);
                        break;
                }
            });

            // 刷新统计按钮
            $('#refreshStats').click(function(){
                table.reload('deviceTaskTable');
                layer.msg('统计数据已刷新', {icon: 1});
            });

            // 自动刷新开关
            $('#autoRefreshBtn').click(function(){
                isAutoRefresh = !isAutoRefresh;
                var btn = $(this);
                if(isAutoRefresh){
                    btn.html('<i class="layui-icon layui-icon-refresh-3"></i> 自动刷新: 开启');
                    btn.removeClass('layui-btn-danger').addClass('layui-btn-primary');
                    startAutoRefresh();
                } else {
                    btn.html('<i class="layui-icon layui-icon-pause"></i> 自动刷新: 关闭');
                    btn.removeClass('layui-btn-primary').addClass('layui-btn-danger');
                    if(autoRefreshTimer) clearInterval(autoRefreshTimer);
                }
            });

            // 显示设备详情
            function showDeviceDetail(deviceData) {
                layer.open({
                    type: 1,
                    title: '设备 ' + deviceData.device_id + ' 详细信息',
                    area: ['80%', '80%'],
                    content: '<div id="deviceDetailContent" style="padding: 20px;"></div>',
                    success: function(){
                        // 加载设备详细任务记录
                        loadDeviceDetailData(deviceData.device_id);
                    }
                });
            }

            // 加载设备详细数据
            function loadDeviceDetailData(deviceId) {
                axios.get('/wsRouter/getDeviceDetailStats?device_id=' + deviceId)
                    .then(function(response) {
                        if(response.data.code === 0) {
                            renderDeviceDetail(response.data.data);
                        } else {
                            $('#deviceDetailContent').html('<p>加载失败: ' + response.data.msg + '</p>');
                        }
                    })
                    .catch(function(error) {
                        $('#deviceDetailContent').html('<p>加载失败: ' + error.message + '</p>');
                    });
            }
            // 渲染设备详情
            function renderDeviceDetail(data) {
                var html = '<table class="layui-table">';
                html += '<thead><tr><th>任务类型</th><th>执行次数</th><th>成功次数</th><th>失败次数</th><th>最后执行时间</th></tr></thead>';
                html += '<tbody>';

                var taskTypes = ['like', 'collect', 'follow', 'comment', 'message', 'recommend', 'comment_like', 'comment_reply'];
                var taskNames = ['点赞', '收藏', '关注', '评论', '私信', '推荐', '评论点赞', '评论回复'];

                taskTypes.forEach(function(type, index) {
                    var taskData = data[type] || {};
                    html += '<tr>';
                    html += '<td>' + taskNames[index] + '</td>';
                    html += '<td>' + (taskData.total || 0) + '</td>';
                    html += '<td>' + (taskData.success || 0) + '</td>';
                    html += '<td>' + (taskData.failed || 0) + '</td>';
                    html += '<td>' + (taskData.last_time || '无记录') + '</td>';
                    html += '</tr>';
                });

                html += '</tbody></table>';
                $('#deviceDetailContent').html(html);
            }

            // 重置设备统计
            function resetDeviceStats(deviceId) {
                layer.confirm('确定要重置设备 ' + deviceId + ' 的统计数据吗？', {
                    icon: 3,
                    title: '确认重置'
                }, function(index) {
                    axios.post('/wsRouter/resetDeviceStats', {device_id: deviceId})
                        .then(function(response) {
                            if(response.data.code === 0) {
                                layer.msg('重置成功', {icon: 1});
                                table.reload('deviceTaskTable');
                            } else {
                                layer.msg('重置失败: ' + response.data.msg, {icon: 2});
                            }
                        })
                        .catch(function(error) {
                            layer.msg('重置失败: ' + error.message, {icon: 2});
                        });
                    layer.close(index);
                });
            }

            // 页面卸载时清除定时器
            $(window).on('beforeunload', function() {
                if(autoRefreshTimer) clearInterval(autoRefreshTimer);
            });

            // 生成演示数据
            function generateDemoData() {
                layer.confirm('确定要生成演示数据吗？这将创建多个设备和大量任务数据。', {
                    icon: 3,
                    title: '确认生成'
                }, function(index) {
                    layer.close(index);

                    var loading = layer.load(1, {
                        shade: [0.3, '#000']
                    });

                    // 使用外部脚本生成演示数据
                    axios.post('/wsRouter/generateDemoData')
                        .then(function(response) {
                            layer.close(loading);
                            layer.msg('演示数据生成中，请稍候...', {icon: 1});
                            // 延迟刷新表格
                            setTimeout(function() {
                                table.reload('deviceTaskTable');
                                updateStatistics();
                            }, 2000);
                        })
                        .catch(function(error) {
                            layer.close(loading);
                            layer.msg('生成失败: ' + error.message, {icon: 2});
                        });
                });
            }

            // 清空所有数据
            function clearAllData() {
                layer.confirm('确定要清空所有数据吗？此操作不可恢复！', {
                    icon: 0,
                    title: '危险操作'
                }, function(index) {
                    layer.close(index);

                    var loading = layer.load(1, {
                        shade: [0.3, '#000']
                    });

                    axios.delete('/wsRouter/device/clearAll', {
                        data: { confirm: true }
                    })
                        .then(function(response) {
                            layer.close(loading);
                            if (response.data.code === 0) {
                                layer.msg('数据清理完成！', {icon: 1});
                                table.reload('deviceTaskTable');
                                updateStatistics();
                            } else {
                                layer.msg('清理失败: ' + response.data.msg, {icon: 2});
                            }
                        })
                        .catch(function(error) {
                            layer.close(loading);
                            layer.msg('清理失败: ' + error.message, {icon: 2});
                        });
                });
            }

            // 批量删除设备
            function batchDeleteDevices() {
                var checkStatus = table.checkStatus('deviceTaskTable');
                var data = checkStatus.data;

                if (data.length === 0) {
                    layer.msg('请选择要删除的设备', {icon: 2});
                    return;
                }

                var deviceIds = data.map(function(item) {
                    return item.device_id;
                });

                layer.confirm('确定要删除选中的 ' + data.length + ' 个设备吗？', {
                    icon: 3,
                    title: '确认删除'
                }, function(index) {
                    layer.close(index);

                    var loading = layer.load(1, {
                        shade: [0.3, '#000']
                    });

                    axios.delete('/wsRouter/device/batchDelete', {
                        data: { deviceIds: deviceIds }
                    })
                        .then(function(response) {
                            layer.close(loading);
                            if (response.data.code === 0) {
                                layer.msg('删除成功！', {icon: 1});
                                table.reload('deviceTaskTable');
                                updateStatistics();
                            } else {
                                layer.msg('删除失败: ' + response.data.msg, {icon: 2});
                            }
                        })
                        .catch(function(error) {
                            layer.close(loading);
                            layer.msg('删除失败: ' + error.message, {icon: 2});
                        });
                });
            }

            // 删除单个设备
            function deleteDevice(data) {
                layer.confirm('确定要删除设备 "' + data.device_name + '" 吗？', {
                    icon: 3,
                    title: '确认删除'
                }, function(index) {
                    layer.close(index);

                    var loading = layer.load(1, {
                        shade: [0.3, '#000']
                    });

                    axios.delete('/wsRouter/device/delete/' + data.device_id)
                        .then(function(response) {
                            layer.close(loading);
                            if (response.data.code === 0) {
                                layer.msg('删除成功！', {icon: 1});
                                table.reload('deviceTaskTable');
                                updateStatistics();
                            } else {
                                layer.msg('删除失败: ' + response.data.msg, {icon: 2});
                            }
                        })
                        .catch(function(error) {
                            layer.close(loading);
                            layer.msg('删除失败: ' + error.message, {icon: 2});
                        });
                });
            }

            // 显示设备详情
            function showDeviceDetail(data) {
                var content = '<div style="padding: 20px;">';
                content += '<h3>设备信息</h3>';
                content += '<p><strong>设备ID:</strong> ' + data.device_id + '</p>';
                content += '<p><strong>设备名称:</strong> ' + data.device_name + '</p>';
                content += '<p><strong>状态:</strong> ' + data.status + '</p>';
                content += '<p><strong>最后活跃:</strong> ' + (data.last_active ? new Date(data.last_active).toLocaleString() : '从未活跃') + '</p>';
                content += '<h3>任务统计</h3>';
                content += '<p><strong>点赞:</strong> ' + (data.like_count || 0) + '</p>';
                content += '<p><strong>收藏:</strong> ' + (data.collect_count || 0) + '</p>';
                content += '<p><strong>关注:</strong> ' + (data.follow_count || 0) + '</p>';
                content += '<p><strong>评论:</strong> ' + (data.comment_count || 0) + '</p>';
                content += '<p><strong>私信:</strong> ' + (data.message_count || 0) + '</p>';
                content += '<p><strong>推荐:</strong> ' + (data.recommend_count || 0) + '</p>';
                content += '<p><strong>评论点赞:</strong> ' + (data.comment_like_count || 0) + '</p>';
                content += '<p><strong>评论回复:</strong> ' + (data.comment_reply_count || 0) + '</p>';
                content += '</div>';

                layer.open({
                    type: 1,
                    title: '设备详情',
                    area: ['500px', '600px'],
                    content: content
                });
            }

            // 初始化
            startAutoRefresh();
        });
    </script>

    <!-- 设备操作按钮模板 -->
    <script type="text/html" id="deviceActionBar">
        <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
    </script>

    <!-- 工具栏模板 -->
    <script type="text/html" id="deviceToolbar">
        <div class="layui-btn-container">
            <button class="layui-btn layui-btn-sm" lay-event="generateDemo">生成演示数据</button>
            <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="clearAll">清空所有数据</button>
            <button class="layui-btn layui-btn-warm layui-btn-sm" lay-event="batchDelete">批量删除</button>
        </div>
    </script>

    <script>
        layui.use('tongYong', function () {
            var tongYong = layui.tongYong;
            tongYong.tongYong1();
        });
    </script>
</body>

</html>