const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
    host: '*************',
    port: 3306,
    user: 'douyin',
    password: '123456',
    database: 'douyin',
    multipleStatements: true,
    ssl: false,
    connectTimeout: 60000,
    acquireTimeout: 60000,
    timeout: 60000
};

// 创建数据库表的SQL语句
const createTablesSQL = `
-- 设备日志表
CREATE TABLE IF NOT EXISTS device_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    device_id VARCHAR(100) NOT NULL,
    device_name VARCHAR(100),
    task_type VARCHAR(50) NOT NULL,
    status ENUM('completed', 'failed', 'running', 'pending') DEFAULT 'pending',
    execution_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_device_id (device_id),
    INDEX idx_task_type (task_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 设备任务统计表
CREATE TABLE IF NOT EXISTS device_task_stats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    device_id VARCHAR(100) NOT NULL,
    device_name VARCHAR(100),
    task_type VARCHAR(50) NOT NULL,
    success_count INT DEFAULT 0,
    failed_count INT DEFAULT 0,
    total_count INT DEFAULT 0,
    last_execution_time TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_device_task (device_id, task_type),
    INDEX idx_device_id (device_id),
    INDEX idx_task_type (task_type),
    INDEX idx_last_execution (last_execution_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 设备状态表
CREATE TABLE IF NOT EXISTS device_status (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    device_id VARCHAR(100) NOT NULL UNIQUE,
    device_name VARCHAR(100),
    status ENUM('online', 'offline', 'busy', 'idle') DEFAULT 'offline',
    last_heartbeat TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_last_heartbeat (last_heartbeat)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 任务队列表
CREATE TABLE IF NOT EXISTS task_queue (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id VARCHAR(100) NOT NULL,
    device_id VARCHAR(100) NOT NULL,
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    priority INT DEFAULT 0,
    task_data JSON,
    assigned_at TIMESTAMP NULL,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_task_id (task_id),
    INDEX idx_device_id (device_id),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 任务表
CREATE TABLE IF NOT EXISTS tasks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id VARCHAR(100) NOT NULL UNIQUE,
    task_name VARCHAR(200) NOT NULL,
    task_type VARCHAR(50) NOT NULL,
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    group_ids JSON,
    device_count INT DEFAULT 0,
    completed_count INT DEFAULT 0,
    failed_count INT DEFAULT 0,
    task_config JSON,
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_task_id (task_id),
    INDEX idx_status (status),
    INDEX idx_task_type (task_type),
    INDEX idx_created_by (created_by),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
`;

// 初始化数据库表
async function initializeTables() {
    let connection;
    
    try {
        console.log('🔗 连接到MySQL数据库...');
        console.log(`📍 服务器: ${dbConfig.host}`);
        console.log(`🗄️  数据库: ${dbConfig.database}`);
        
        connection = await mysql.createConnection(dbConfig);
        
        console.log('✅ 数据库连接成功');
        
        // 执行建表语句
        console.log('🏗️  开始创建数据库表...');
        await connection.query(createTablesSQL);
        
        console.log('✅ 数据库表创建完成');
        
        // 验证表是否创建成功
        console.log('🔍 验证表结构...');
        const [tables] = await connection.query(`
            SELECT TABLE_NAME, TABLE_ROWS, DATA_LENGTH, INDEX_LENGTH
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = ? 
            AND TABLE_NAME IN ('device_logs', 'device_task_stats', 'device_status', 'task_queue', 'tasks')
            ORDER BY TABLE_NAME
        `, [dbConfig.database]);
        
        console.log('\n📊 数据库表信息:');
        tables.forEach(table => {
            console.log(`  📋 ${table.TABLE_NAME}: ${table.TABLE_ROWS || 0} 行, 数据大小: ${(table.DATA_LENGTH / 1024).toFixed(2)} KB`);
        });
        
        // 插入测试数据
        console.log('\n🧪 插入测试数据...');
        await insertTestData(connection);
        
        console.log('\n🎉 数据库初始化完成！');
        
    } catch (error) {
        console.error('❌ 数据库初始化失败:', error.message);
        console.error('错误详情:', error);
        
        if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            console.log('\n💡 解决方案:');
            console.log('1. 检查数据库用户名和密码是否正确');
            console.log('2. 确保用户有足够的权限');
            console.log('3. 在MySQL中执行以下命令:');
            console.log(`   GRANT ALL PRIVILEGES ON ${dbConfig.database}.* TO '${dbConfig.user}'@'%';`);
            console.log('   FLUSH PRIVILEGES;');
        }
        
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔒 数据库连接已关闭');
        }
    }
}

// 插入测试数据
async function insertTestData(connection) {
    try {
        // 插入测试设备状态
        await connection.query(`
            INSERT IGNORE INTO device_status (device_id, device_name, status, last_heartbeat)
            VALUES 
            ('test_device_001', '测试设备001', 'online', NOW()),
            ('test_device_002', '测试设备002', 'offline', DATE_SUB(NOW(), INTERVAL 1 HOUR))
        `);
        
        // 插入测试任务统计
        await connection.query(`
            INSERT IGNORE INTO device_task_stats (device_id, device_name, task_type, success_count, failed_count, total_count, last_execution_time)
            VALUES 
            ('test_device_001', '测试设备001', 'like', 10, 1, 11, NOW()),
            ('test_device_001', '测试设备001', 'collect', 5, 0, 5, NOW()),
            ('test_device_002', '测试设备002', 'follow', 3, 2, 5, DATE_SUB(NOW(), INTERVAL 30 MINUTE))
        `);
        
        console.log('✅ 测试数据插入成功');
        
    } catch (error) {
        console.log('⚠️  测试数据插入失败:', error.message);
    }
}

// 运行初始化
console.log('🚀 开始初始化MySQL数据库表...');
initializeTables();
