const express = require('express');
const router = express.Router();
const debug = require('debug')('a-sokio-yun:server');
const axios = require('axios')

const mongoose = require('../db/db.js');
const deviceShe = require('../db/deviceShe')
const messageShe = require('../db/messageShe')
const { pool } = require('../db/mysql'); // 正确导入连接池

let userMod = require('../db/userMod')
// const userLogMod = require('../db/userLogMod')

const clients = new Map();


// qywxsend("jz服务器重启")
//服务器重启,所有设备掉线
userMod.find({}).then(docs => {
    if (docs && docs.length > 0) {
        docs.forEach(doc => {
            let deviceMod = mongoose.model('device_' + doc.userName, deviceShe)
            deviceMod.updateMany({}, { taskStatus: "掉线" }, { upsert: false }).then()
        })
    }
}).catch(err => {
    console.log(err)
})

// 创建 WebSocket 路由
router.ws('/:userName/:deviceName/:device_UUID/:device_height/:device_width/:taskStatus', (ws, req) => {

    const device_dic = req.params

    // debug(device_dic)
    
    const deviceName = device_dic.deviceName;//对应云控编号
    // const device_UUID = device_dic.device_UUID;
    // const device_height = device_dic.device_height;
    // const device_width = device_dic.device_width;
    const userName = device_dic.userName//对应云控账号
    let wsid = ""
    // let userName = req.session.username;//浏览器
    // if (userName) {
    //     wsid = "liuLanQi_" + userName
    //     debug(`浏览器上线: ${wsid}`);
    // }//这个方案不知道怎么回事突然获取不到用户名
    // debug("浏览器:" + req.session.username)
    if (deviceName == "liuLanQi") {
        wsid = "liuLanQi_" + userName
        debug(`浏览器上线: ${wsid}`);
    } else {
        // 获取设备信息 并存储到数据库
        wsid = userName + "_" + deviceName
        console.log(`设备链接: ${wsid}`);
        let d = new Date() //Date.now()
        let dataStr = d.getMonth() + 1 + "月" + d.getDate() + "日" + d.getHours() + ":" + d.getMinutes() + ":" + d.getSeconds()
        //这里taskName不传

        let upData = { taskStatus: device_dic.taskStatus, socketId: wsid, updateTime: dataStr, device_width: device_dic.device_width, device_height: device_dic.device_height, device_UUID: device_dic.device_UUID }

        let deviceMod = mongoose.model('device_' + userName, deviceShe)
        deviceMod.updateOne({ deviceName }, upData, { upsert: true }).then()
    }

    // 将 WebSocket 实例存储起来
    clients.set(wsid, ws);

    // 初始化isAlive标记为true
    ws.isAlive = true;
    // 监听到信息 表示连接存活
    ws.on('pong', () => {
        debug('收到' + wsid + '的 Pong 消息');
        ws.isAlive = true;
    });

    // 实现每隔30秒发送一次Ping消息 
    const heartbeatTimer = setInterval(() => {
        if (ws.isAlive === false) {
            console.log('服务器,' + wsid + '的心跳异常???')
            clearInterval(heartbeatTimer);
            // qywxsend('服务器,' + wsid + '的心跳异常???')
            return ws.terminate();
        }

        ws.isAlive = false;
        debug('服务器,给' + wsid + '发送心跳包')
        ws.ping('');
    }, 30000);


    // 监听客户端连接,回调函数会传递本次连接的ws
    // 信息展示
    // 监听消息事件

    ws.on("message", msg => {
        // console.log(msg,'1111')
        // debug(`收到${wsid}的消息: ${msg}`);
        onMessage(userName, wsid, ws, msg)
    })

    // 监听关闭事件
    ws.on('close', (code, reason) => {
        // console.log("掉线");
        // 关闭心跳包检测是否存活
        clearInterval(heartbeatTimer);
        if (clients.get(wsid) !== ws) {
            console.log(`旧ws掉线: ${wsid},code:${code},reason:${reason},已重连`);
            return
        }

        clients.delete(wsid);// 从 Map 中删除 WebSocket 实例
        console.log(`ws断开: ${wsid},code:${code},reason:${reason},踢下线`);
        // 如果是投屏断开,则向手机发送取消发送截图的命令
        if (wsid == "liuLanQi_root") {
            // debug("浏览器关闭投屏:" + userName)
            for (const [id, client] of clients) {
                client.send(JSON.stringify({ type: "touPing_stop" }))
            }
        } else if (wsid.includes("liuLanQi_")) {
            // debug("浏览器关闭投屏:" + userName)
            for (const [id, client] of clients) {
                if (id.startsWith(userName + "_")) {
                    client.send(JSON.stringify({ type: "touPing_stop" }))
                }
            }
        } else {//if (wsid.indexOf("_") > -1)
            //  更新设备状态为掉线
            // qywxsend(`ws断开: ${wsid},code:${code},reason:${reason}`)
            let deviceMod = mongoose.model('device_' + userName, deviceShe)
            deviceMod.updateOne({ socketId: wsid }, { taskStatus: "掉线" }, { upsert: false }).then()
        }
    })
});

/**
 * @description: 
 * @param {*} userName 登录用户账号
 * @param {*} wsid 设备信息
 * @param {*} ws WebSocket实例
 * @param {*} msg WebSocket on('message') 监听到的数据
 * @return {*}
 */
async function onMessage(userName, wsid, ws, msg) {
    // debug(wsid, msg);

    // 判断是否二进制数据类型 如果是二进制则是投屏传输图片数据
    if (Buffer.isBuffer(msg)) {
        // debug("图片二进制数据")
        // debug("图片大小:", msg.length)
        // let wsidarr = []
        // if (userName == "root") {
        //     wsidarr = ["liuLanQi_" + userName]
        // } else {
        //     wsidarr = ["liuLanQi_" + userName, "liuLanQi_root"]
        // }
        let wsidarr = ["liuLanQi_" + userName]
        sendToClients(wsidarr, JSON.stringify({ type: "image/jpeg", wsid: wsid, binaryArr: msg }))
        return
    }

    let dic = isJSON(msg)
    if (!dic) {
        debug(wsid, "的消息无须解析", msg)
        return
    }
    // debug(wsid, dic);

    // 浏览器发送命令开启投屏//服务器转发给设备
    if (dic.type == "touPing_start") { // && wsid.includes("liuLanQi_")
        sendToClients(dic.socketIds, JSON.stringify({ type: "touPing_start", touPingDic: dic.touPingDic }))
    } else if (dic.type == "touPing_stop") { // 关闭投屏
        sendToClients(dic.socketIds, JSON.stringify({ type: "touPing_stop" }))
    } else if (dic.type == "startAction") { // 浏览器下发操作屏幕指令
        sendToClients(dic.wsid, JSON.stringify(dic))
    } else if (dic.type == "状态") {
        let data = dic.data
        let d = new Date() //Date.now()
        let updateTime = d.getMonth() + 1 + "月" + d.getDate() + "日" + d.getHours() + ":" + d.getMinutes() + ":" + d.getSeconds()
        let upData = { taskStatus: data.taskStatus, updateTime: updateTime, taskName: data.taskName }
        debug("用户:" + data.userName + "的设备:" + data.deviceName + "更新状态了")
        let deviceMod = mongoose.model('device_' + data.userName, deviceShe)
        deviceMod.updateOne({ deviceName: data.deviceName }, upData, { upsert: true }).then()
    } else if (dic.type == "信息") {
        // debug("更新信息:" + dic.data);
        debug("用户:" + wsid + "更新信息")
        let deviceMod = mongoose.model('device_' + userName, deviceShe)
        deviceMod.updateOne({ socketId: wsid }, { deviceMsg: dic.data }, { upsert: true }).then()
    } else if (dic.type == "ping") {
        ws.isAlive = true;
        ws.send(JSON.stringify({ type: "send" }))

    } else if (dic.type == "ping_test") {

    } else if (dic.type == "send") {
        try {
            console.log('收到消息数据:', JSON.stringify(dic, null, 2));
            
            // 数据验证
            if (!dic.phoneNumber || !dic.username || !dic.links) {
                const errorMsg = `缺少必要参数: ${JSON.stringify({
                    phoneNumber: dic.phoneNumber,
                    username: dic.username,
                    links: dic.links
                })}`;
                console.error(errorMsg);
                return;
            }

            // 数据格式化
            const messageData = {
                type: dic.type || 'unknown',
                phoneNumber: dic.phoneNumber.toString().trim(),
                username: dic.username.toString().trim(),
                links: dic.links.toString().trim()
            };

            console.log('格式化后的消息数据:', messageData);
            
            // 保存消息到ws_link_data表
            const connection = await pool.getConnection();
            const [result] = await connection.query(
                'INSERT INTO ws_link_data (phoneNumber, username, links) VALUES (?, ?, ?)',
                [messageData.phoneNumber, messageData.username, messageData.links]
            );
            connection.release();
            console.log(`消息保存成功, ID: ${result.insertId}`);
            
            // 返回成功响应
            ws.send(JSON.stringify({
                type: "send_response",
                status: "success",
                messageId: messageId
            }));
            
        } catch (err) {
            console.error('保存消息失败:', {
                error: err.message,
                stack: err.stack,
                originalData: dic
            });
            
            // 返回错误响应
            ws.send(JSON.stringify({
                type: "send_response",
                status: "error",
                message: err.message
            }));
        }
    } else {
        console.log("无效数据");
        console.log(dic);
    }
}


// 发送数据给指定的 WebSocket 实例
function sendToClients(clientIds, data) {
    console.log('准备发送数据给设备:', clientIds);
    console.log('数据内容长度:', data ? data.length : 0);

    // 验证clientIds是否为数组
    if (!Array.isArray(clientIds)) {
        console.error('clientIds必须为数组');
        return;
    }

    // 验证数据是否为字符串
    if (typeof data !== 'string') {
        try {
            data = JSON.stringify(data);
        } catch (e) {
            console.error('数据格式化失败:', e);
            return;
        }
    }

    let successCount = 0;
    let failCount = 0;

    for (const clientId of clientIds) {
        if (!clientId || typeof clientId !== 'string') {
            console.error('无效的clientId:', clientId);
            failCount++;
            continue;
        }

        const ws = clients.get(clientId);
        if (ws && ws.readyState === ws.OPEN) {
            try {
                ws.send(data);
                console.log(`成功发送给设备 ${clientId}`);
                successCount++;
            } catch (e) {
                console.error(`发送给设备 ${clientId} 失败:`, e);
                failCount++;
                clients.delete(clientId);
            }
        } else {
            console.error(`设备 ${clientId} 未连接或连接未就绪`);
            failCount++;
        }
    }

    console.log(`发送结果: 成功 ${successCount} 个, 失败 ${failCount} 个`);

    // 如果全部失败，记录详细错误
    if (successCount === 0 && failCount > 0) {
        console.error('所有消息发送失败，请检查WebSocket连接状态');
    }

    // 注意：数据处理逻辑已经在调用sendToClients之前完成
    // 这里只负责简单的消息发送，不再进行数据处理
}


// 判断是否为json格式 如果解析后数据为obj且不为空 则正常返回数据
function isJSON(str) {
    if (typeof str == 'string') {
        try {
            var obj = JSON.parse(str);
            if (typeof obj == 'object' && obj) {
                return obj;
            } else {
                return false;
            }
        } catch (e) {
            debug(e)
            return false;
        }
    } else {
        console.log(typeof str + "????????????????????????");
    }
}


// // 解析请求体
// router.use(bodyParser.json());
// router.use(bodyParser.urlencoded({ extended: true }));

// 处理 HTTP POST 请求

// 判断用户是否在线 在线得话 开始使用WebScoket向脚本发送任务
router.post('/faTast', (req, res) => {
    let { data } = req.body
    // debug("data", req.body)
    // let userName = data.userName
    if (data.userName) {
        faTask(res, data, data.userName)
    } else {
        res.json({
            code: -1,
            msg: "失败:用户登录失效"
        })
    }
})

async function faTask(res, data, userName) {
    //向后台发送给提交成功
    console.log("服务器收到任务,开始分发")
    //给设备arr发任务,并记录日志
    try {
        // 处理养号任务

        
        // 其他任务处理

        // debug("data.taskData.taskID", data.taskData.taskID)
        // debug(data.taskData.taskID ? { taskID: data.taskData.taskID } : data.taskData)
        // debug(data.taskData.taskID || data.taskData)

        // 构建完整的任务数据，确保所有参数都传递
        let taskTask = JSON.stringify({
            type: "deviceTask",
            taskId: data.taskId || null,
            taskMode: data.taskMode || null,
            serverData: {
                taskName: data.taskName,
                taskData: data.taskData, // 传递完整的taskData对象
                timeStamp: data.timeStamp,
                // 添加额外的模式信息
                mode: data.taskMode,
                originalData: data // 保留原始数据以备调试
            }
        })

        console.log('=== wsRouter处理任务数据 ===');
        console.log('接收到的data:', JSON.stringify(data, null, 2));
        console.log('构建的taskTask:', taskTask);
        console.log('========================');
        let wsIds = [] // 定义一个数组，用于保存要发送到的WebSocket客户端的ID
        if (data.socketIdArr && data.socketIdArr.length > 0) {
            debug('有附加设备')
            wsIds = data.socketIdArr
        }

        if (data.groupDocArr && data.groupDocArr.length > 0) {
            // debug('有分组全选')
            // debug(data.groupDocArr)
            let deviceMod = mongoose.model('device_' + userName, deviceShe)
            for (let groupDoc of data.groupDocArr) {
                // debug(groupDoc)
                let devices = await deviceMod.find({ groupId: groupDoc.groupId })//.exec()
                for (let device of devices) {
                    //给指定得客户端发送消息1
                    // debug("添加到任务数组:用户的" + userName + "[" + groupDoc.groupName + "]组的:" + device.deviceName + ",wsid:" + device.socketId)
                    if (!wsIds.includes(device.socketId)) {
                        await wsIds.push(device.socketId)
                    }
                };
            }
        }

        //记录日志
        // let d = new Date()
        // debug(userObj)
        // new userLogMod({
        //     userName: userName,
        //     logStr: "所选设备[" + wsIds + "],任务名[" + data.taskName + "],任务参数[" + JSON.stringify(data.taskData) + "]",
        //     logTime: d.getFullYear() + "年" + (d.getMonth() + 1) + "月" + d.getDate() + "日" + d.getHours() + ":" + d.getMinutes() + ":" + d.getSeconds()
        // }).save()

        //发送任务 - 根据任务类型进行特殊处理
        debug("下发任务给wsIds", wsIds)

        // 检查是否需要特殊的数据分发处理
        if (data.taskName === "提交评论区操作数据") {
            // 处理douyin514任务 - 统一的数据分发逻辑
            handleDouyin514Task(wsIds, data, taskTask);
        } else {
            // 其他任务直接发送
            sendToClients(wsIds, taskTask);
        }

        res.json({
            code: 1,
            msg: "成功"
        })
    } catch (err10) {
        res.json({
            code: -1,
            msg: "下发任务出错:" + err10.toString()
        })
    }
}

// 处理douyin514任务的数据分发逻辑
function handleDouyin514Task(wsIds, data, originalTaskTask) {
    try {
        const taskData = data.taskData;
        const keywords = taskData.keyword ? taskData.keyword.split("\n").filter(Boolean) : [];
        const comments = taskData.commentContent ? taskData.commentContent.split("\n").filter(Boolean) : [];
        const links = taskData.linkUrl ? taskData.linkUrl.split("\n").filter(Boolean) : [];

        // 检查是否开启了"@指定用户"功能
        const isCommentAtIdEnabled = taskData.commentAtIdSwitch === 'on' || taskData.commentAtIdSwitchValue === 'on';

        // 处理图片数据
        let images = [];
        if (taskData.imageData) {
            console.log('原始imageData:', taskData.imageData);
            try {
                // 尝试解析为数组或直接按逗号分割
                if (taskData.imageData.includes(',')) {
                    images = taskData.imageData.split(',').filter(img => img.trim());
                } else {
                    images = [taskData.imageData];
                }
            } catch (e) {
                console.error('处理图片数据失败:', e);
                images = [];
            }
        }

        console.log(`数据分发统计: ${wsIds.length}个设备, ${keywords.length}个关键词, ${comments.length}个评论, ${links.length}个链接, ${images.length}个图片`);
        console.log(`@指定用户功能: ${isCommentAtIdEnabled ? '开启' : '关闭'}`);

        // 统一的分发逻辑：单设备分发所有数据，多设备均匀分配
        if (wsIds.length === 1) {
            // 单设备情况：分发所有数据给唯一设备
            const clientId = wsIds[0];
            const ws = clients.get(clientId);
            if (ws) {
                const parsedData = JSON.parse(originalTaskTask);
                const clientData = JSON.parse(JSON.stringify(parsedData));

                // 分发所有数据，用 | 分隔符连接
                if (keywords.length > 0) {
                    clientData.serverData.taskData.keyword = keywords.join('|');
                }

                // 评论内容处理：如果开启@指定用户，则传递所有@用户ID
                if (comments.length > 0) {
                    if (isCommentAtIdEnabled) {
                        // @指定用户模式：传递所有@用户ID，用换行符分隔
                        clientData.serverData.taskData.commentContent = comments.join('\n');
                        console.log(`@指定用户模式: 传递所有@用户ID，共${comments.length}个用户`);
                        console.log(`@用户ID列表:\n${comments.join('\n')}`);
                    } else {
                        // 普通模式：分发所有评论内容，用|分隔
                        clientData.serverData.taskData.commentContent = comments.join('|');
                    }
                }

                if (links.length > 0) {
                    clientData.serverData.taskData.linkUrl = links.join('|');
                }
                if (images.length > 0) {
                    clientData.serverData.taskData.imageData = images.join(',');
                }

                // 保持原有功能开关状态
                Object.keys(taskData).forEach(key => {
                    if (key.endsWith('Switch') || key.endsWith('SwitchValue')) {
                        clientData.serverData.taskData[key] = taskData[key];
                    }
                });

                console.log(`单设备分发完成: 设备${clientId}收到所有数据`);
                ws.send(JSON.stringify(clientData));
            }
        } else {
            // 多设备情况：均匀分配数据
            const clientCount = wsIds.length;
            const keywordPerClient = Math.max(1, Math.ceil(keywords.length / clientCount));
            const commentPerClient = Math.max(1, Math.ceil(comments.length / clientCount));
            const linkPerClient = Math.max(1, Math.ceil(links.length / clientCount));
            const imagePerClient = Math.max(1, Math.ceil(images.length / clientCount));

            wsIds.forEach((clientId, index) => {
                const ws = clients.get(clientId);
                if (ws) {
                    const parsedData = JSON.parse(originalTaskTask);
                    const clientData = JSON.parse(JSON.stringify(parsedData));

                    // 分配关键词
                    if (keywords.length > 0) {
                        const start = index * keywordPerClient;
                        const end = Math.min(start + keywordPerClient, keywords.length);
                        const assignedKeywords = keywords.slice(start, end);
                        if (assignedKeywords.length > 0) {
                            clientData.serverData.taskData.keyword = assignedKeywords.join('|');
                        }
                    }

                    // 分配评论内容：如果开启@指定用户，则所有设备都收到所有@用户ID
                    if (comments.length > 0) {
                        if (isCommentAtIdEnabled) {
                            // @指定用户模式：所有设备都收到所有@用户ID，用换行符分隔
                            clientData.serverData.taskData.commentContent = comments.join('\n');
                            console.log(`@指定用户模式: 设备${clientId}收到所有@用户ID，共${comments.length}个用户`);
                        } else {
                            // 普通模式：分配不同的评论内容，用|分隔
                            const start = index * commentPerClient;
                            const end = Math.min(start + commentPerClient, comments.length);
                            const assignedComments = comments.slice(start, end);
                            if (assignedComments.length > 0) {
                                clientData.serverData.taskData.commentContent = assignedComments.join('|');
                            }
                        }
                    }

                    // 分配链接
                    if (links.length > 0) {
                        const start = index * linkPerClient;
                        const end = Math.min(start + linkPerClient, links.length);
                        const assignedLinks = links.slice(start, end);
                        if (assignedLinks.length > 0) {
                            clientData.serverData.taskData.linkUrl = assignedLinks.join('|');
                        }
                    }

                    // 分配图片
                    if (images.length > 0) {
                        const start = index * imagePerClient;
                        const end = Math.min(start + imagePerClient, images.length);
                        const assignedImages = images.slice(start, end);
                        if (assignedImages.length > 0) {
                            clientData.serverData.taskData.imageData = assignedImages.join(',');
                        }
                    }

                    // 保持原有功能开关状态
                    Object.keys(taskData).forEach(key => {
                        if (key.endsWith('Switch') || key.endsWith('SwitchValue')) {
                            clientData.serverData.taskData[key] = taskData[key];
                        }
                    });

                    console.log(`多设备分发: 设备${clientId}(${index + 1}/${clientCount})收到分配的数据`);
                    ws.send(JSON.stringify(clientData));
                }
            });
        }
    } catch (error) {
        console.error('handleDouyin514Task 处理失败:', error);
        // 如果处理失败，回退到原始发送方式
        sendToClients(wsIds, originalTaskTask);
    }
}

router.post('/send/:clientId', (req, res) => {
    const clientId = req.params.clientId; // 获取URL路径中的clientId参数
    const data = req.body.data; // 获取请求体中的data字段

    debug(`Sending data to: ${clientId}`); // 打印日志，表示正在向指定的clientId发送数据
    debug(`Data: ${data}`); // 打印日志，表示要发送的数据内容

    // 发送数据
    sendToClients([clientId], data); // 调用sendToClients()方法，将数据发送给指定的clientId
    res.sendStatus(200);// 返回状态码200表示成功处理请求
});

router.get('/wss', function (req, res) {
    let zhangHao = req.query.z // 获取查询参数中的账号（用户名）
    if (zhangHao) { // 如果账号存在
        let deviceNum = 0 // 已上线设备数量
        for (const id of clients.keys()) { // 判断连接ID是否以当前账号开头
            if (id.startsWith(zhangHao + "_")) { // 判断连接ID是否以当前账号开头
                deviceNum = deviceNum + 1 // 判断连接ID是否以当前账号开头
            }
        }
        res.send(`ws(${zhangHao}):${deviceNum}个`) // 返回格式化的已上线设备数量信息
    } else {
        res.send(`ws(总):${clients.size}个`) // 如果账号不存在，返回格式化的总连接数量信息
    }
});

//上线前先检测
router.get('/wsid', async function (req, res) {
    let deviceName = req.query.id
    let userName = req.query.userName
    let wsid = `${userName}_${deviceName}`
    try {
        //先判断有没有此账户,再判断上线数量,最后判断有没有重复
        const doc = await userMod.findOne({ userName })
        if (!doc) {
            // debug(userName + "用户不存在");
            return res.json({
                code: -1,
                msg: "用户不存在"
            })
        }

        //用户存在
        //判断上线数量两种方案,(暂用第二种)
        //第一种通过数据库判断,缺点1占用数据库线程.缺点2需要把没用的设备删除
        //第二种通过遍历ws,例如root,通过di是否indexOf("root_")然后+1来计算.缺点遍历完才知道个数.占用内存?

        let deviceNum = 0 // 已上线设备数量
        const maxNum = doc.userDeviceNum // 设定的此用户占控数量
        debug(`本次上线id(${wsid}),用户(${userName})的最大占控数量${maxNum}个`)
        const wwwsss = clients.get(wsid) // 根据连接ID获取已存在的连接对象
        if (wwwsss) { // 如果连接ID已存在的连接对象
            console.log(`连接ID${wsid}已存在的连接对象,关闭先前的连接`)
            wwwsss.terminate(); // 关闭先前的连接
            // throw new Error(wsid + "有重复")
            return res.json({
                code: -1,
                msg: "疑似重复,已剔除老设备"
            })
        }

        for (const id of clients.keys()) {
            console.log("id", id);
            if (id.startsWith(userName + "_")) { // 判断连接ID是否以当前用户名开头
                deviceNum = deviceNum + 1 // 计算已上线设备数量
            }
        }


        debug(userName + ':已有设备数量:' + deviceNum)

        if (deviceNum >= maxNum) {  // 如果已上线设备数量超过设定的上限
            return res.json({
                code: -1,
                msg: `设备超过设定数量:${maxNum}`
            })
        } else {
            return res.json({
                code: 1,
                msg: "可以上线"
            })
        }
    } catch (error) {
        return res.json({
            code: -1,
            msg: "服务器:" + error.message
        })
    }

})
//手机端ws验证代码
// function yanZheng() {
//     // for (let i = 3; i > 0; i--) {
//     // while (true) {
//     logd("验证中..",)
//     try {
//         let resJson = http.get("http://" + server_api + "/wsRouter/wsid?userName=" + jz_yun.云控账号 + "&id=" + jz_yun.云控编号).body.json()
//         if (resJson) {
//             // logd(JSON.stringify(resJson))
//             if (resJson.code == 1) {
//                 // logd("验证成功.连接云控后台中..")
//                 return true
//             } else {
//                 // loge(resJson.msg, "20秒后重试")
//                 loge(resJson.msg)
//                 alert(resJson.msg + ",请重试")
//                 exit()
//             }
//         } else {
//             logi("登录错误,重试中")
//             // exit()
//         }
//     } catch (error) {
//         loge("验证错误:", error)
//         // alert("验证错误:【" + error.toString() + "】行:" + error.lineNumber)
//         // exit()
//     }
//     sleep(20000)
//     // }

// }

async function qywxsend(str) {
    try {
        let response = await axios.post("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d817ae9d-ed16-46d4-bf2a-cdcfc8ff50d0", {
            msgtype: 'text',
            text: { content: str }
        })
        if (response.data.errmsg == "ok") {
            // log.info("企微发信成功")
            return true
        }
    } catch (error) {
        log.error("企微发信错误:", error)
    }
}

// 添加查询messages数据的接口
router.get('/getMessages', async (req, res) => {
    try {
        const connection = await pool.getConnection();
        const [rows] = await connection.query('SELECT * FROM messages ORDER BY create_time DESC');
        connection.release();
        res.json({
            code: 1,
            data: rows
        });
    } catch (err) {
        console.error('查询消息失败:', err);
        res.status(500).json({
            code: -1,
            msg: '查询消息失败'
        });
    }
});

// 查询所有链接数据
router.get('/queryAllLinkData', async (req, res) => {
    console.log('queryAllLinkData接口被调用');
    try {
        console.log('获取数据库连接');
        const connection = await pool.getConnection();
        console.log('执行SQL查询');
        const [rows] = await connection.query('SELECT * FROM ws_link_data ORDER BY created_at DESC');
        console.log('查询结果:', rows);
        connection.release();
        console.log('返回数据');
        res.json({
            code: 1,
            data: rows
        });
    } catch (err) {
        console.error('查询链接数据失败:', {
            error: err.message,
            stack: err.stack
        });
        res.status(500).json({
            code: -1,
            msg: '查询链接数据失败'
        });
    }
});

// WebSocket连接状态检查
function checkWebSocketConnections() {
    console.log('当前活跃连接:', clients.size);
    clients.forEach((ws, wsid) => {
        console.log(`连接ID: ${wsid}, 状态: ${ws.readyState}`);
    });
}

// 定时检查连接状态
setInterval(checkWebSocketConnections, 60000);

// 新增日志查询接口(带分页和筛选)
router.get('/queryLogs', async (req, res) => {
    console.log('queryLogs接口被调用，参数:', req.query);
    try {
        const { page = 1, limit = 20, device_id } = req.query;
        const offset = (page - 1) * limit;
        
        console.log('获取数据库连接');
        const connection = await pool.getConnection();
        
        let query = 'SELECT * FROM device_logs';
        let countQuery = 'SELECT COUNT(*) as total FROM device_logs';
        const params = [];
        
        if (device_id) {
            query += ' WHERE device_id = ?';
            countQuery += ' WHERE device_id = ?';
            params.push(device_id);
        }
        
        query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
        params.push(parseInt(limit), offset);
        
        console.log('执行SQL查询:', query);
        const [rows] = await connection.query(query, params);
        const [[{total}]] = await connection.query(countQuery, params.slice(0, -2));
        
        connection.release();
        
        console.log('返回分页数据');
        res.json({
            code: 1,
            data: {
                list: rows,
                total,
                page: parseInt(page),
                limit: parseInt(limit)
            }
        });
    } catch (err) {
        console.error('查询日志失败:', {
            error: err.message,
            stack: err.stack
        });
        res.status(500).json({
            code: -1,
            msg: '查询日志失败'
        });
    }
});

router.post('/mylogs', async (req, res) => {
    console.log('设备日志插入接口被调用，数据:', req.body);
    const { device_id, task_type, status, execution_data } = req.body;

    // 验证必填字段
    if (!device_id || !status) {
        console.log('缺少必填字段');
        return res.status(400).json({
            code: -1,
            msg: 'device_id和status是必填字段'
        });
    }
    
    // 验证status枚举值
    const validStatuses = ['pending', 'running', 'completed', 'failed'];
    if (!validStatuses.includes(status)) {
        console.log('无效的status值:', status);
        return res.status(400).json({
            code: -1,
            msg: `status必须是以下值之一: ${validStatuses.join(', ')}`
        });
    }

    try {
        const connection = await pool.getConnection();
        console.log('获取数据库连接成功');
        
        try {
            const [result] = await connection.query(
                'INSERT INTO device_logs (device_id, task_type, status, execution_data) VALUES (?, ?, ?, ?)',
                [device_id, task_type || 'system', status, execution_data || null]
            );
            
            console.log(`成功插入日志，ID: ${result.insertId}`);
            
            res.json({
                code: 0,
                msg: '日志插入成功',
                data: { id: result.insertId }
            });
        } finally {
            connection.release();
            console.log('释放数据库连接');
        }
    } catch (err) {
        console.error('插入日志失败:', err);
        res.status(500).json({
            code: -1,
            msg: err.message
        });
    }
});

router.get('/mylogs', async (req, res) => {
    console.log('设备日志查询接口被调用，参数:', req.query);
    let { device_id, status, start_time, end_time } = req.query;

    try {
        // 构建查询条件
        let whereClause = 'WHERE 1=1';
        let params = [];
        
        if (device_id) {
            whereClause += ' AND device_id = ?';
            params.push(device_id);
        }
        if (status) {
            whereClause += ' AND status = ?';
            params.push(status);
        }
        if (start_time) {
            whereClause += ' AND created_at >= ?';
            params.push(start_time);
        }
        if (end_time) {
            whereClause += ' AND created_at <= ?';
            params.push(end_time);
        }

        // 执行查询（移除 LIMIT 和 OFFSET）
        const [results] = await pool.query(`
            SELECT 
                id, device_id, status, execution_data,
                DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s') AS created_at
            FROM device_logs
            ${whereClause}
            ORDER BY created_at DESC
        `, params);

        const [count] = await pool.query(`
            SELECT COUNT(*) AS total FROM device_logs ${whereClause}
        `, params);

        res.json({
            code: 0,
            data: {
                list: results,
                total: count[0].total
            }
        });
    } catch (err) {
        console.error('查询设备日志失败:', err);
        res.status(500).json({
            code: -1,
            msg: '查询设备日志失败'
        });
    }
});

// 获取设备任务统计数据
router.get('/getDeviceTaskStats', async (req, res) => {
    try {
        const { device_id, status, page = 1, limit = 20, summary } = req.query;

        // 如果请求汇总数据
        if (summary === 'true') {
            return await getTaskSummary(res);
        }

        // 直接使用MySQL连接池查询数据
        if (!pool) {
            return res.json({
                code: -1,
                msg: 'MySQL连接池未初始化',
                data: []
            });
        }

        const connection = await pool.getConnection();

        // 构建查询条件
        let whereClause = 'WHERE 1=1';
        let params = [];

        if (device_id) {
            whereClause += ' AND device_id = ?';
            params.push(device_id);
        }

        if (status) {
            whereClause += ' AND status = ?';
            params.push(status);
        }

        // 查询设备任务统计数据（使用device_task_stats表）- 兼容版本
        let sql;
        try {
            // 先尝试查询表结构，检查字段是否存在
            const [columns] = await connection.query('DESCRIBE device_task_stats');
            const hasDeviceName = columns.some(col => col.Field === 'device_name');

            // 构建兼容的SQL查询
            sql = `
                SELECT
                    s.device_id,
                    ${hasDeviceName ? 'MAX(s.device_name) as device_name,' : 's.device_id as device_name,'}
                    COALESCE(MAX(ds.status), 'offline') as status,
                    COALESCE(MAX(ds.last_heartbeat), MAX(s.updated_at)) as last_active,
                    SUM(CASE WHEN s.task_type = 'like' THEN s.total_count ELSE 0 END) as like_count,
                    SUM(CASE WHEN s.task_type = 'collect' THEN s.total_count ELSE 0 END) as collect_count,
                    SUM(CASE WHEN s.task_type = 'follow' THEN s.total_count ELSE 0 END) as follow_count,
                    SUM(CASE WHEN s.task_type = 'comment' THEN s.total_count ELSE 0 END) as comment_count,
                    SUM(CASE WHEN s.task_type = 'message' THEN s.total_count ELSE 0 END) as message_count,
                    SUM(CASE WHEN s.task_type = 'recommend' THEN s.total_count ELSE 0 END) as recommend_count,
                    SUM(CASE WHEN s.task_type = 'comment_like' THEN s.total_count ELSE 0 END) as comment_like_count,
                    SUM(CASE WHEN s.task_type = 'comment_reply' THEN s.total_count ELSE 0 END) as comment_reply_count,
                    -- 新增的评论区操作统计（兼容处理）
                    SUM(CASE WHEN s.task_type = 'commentLike' THEN s.total_count ELSE 0 END) as commentLike_count,
                    SUM(CASE WHEN s.task_type = 'commentReply' THEN s.total_count ELSE 0 END) as commentReply_count,
                    SUM(CASE WHEN s.task_type = 'commentUserLike' THEN s.total_count ELSE 0 END) as commentUserLike_count,
                    SUM(CASE WHEN s.task_type = 'commentUserRecommend' THEN s.total_count ELSE 0 END) as commentUserRecommend_count,
                    SUM(CASE WHEN s.task_type = 'commentUserCollect' THEN s.total_count ELSE 0 END) as commentUserCollect_count,
                    SUM(CASE WHEN s.task_type = 'commentUserMessage' THEN s.total_count ELSE 0 END) as commentUserMessage_count
                FROM device_task_stats s
                LEFT JOIN device_status ds ON s.device_id = ds.device_id
                ${whereClause.replace('device_id', 's.device_id').replace('status', 'ds.status')}
                GROUP BY s.device_id
                ORDER BY COALESCE(MAX(ds.last_heartbeat), MAX(s.updated_at)) DESC
                LIMIT ? OFFSET ?
            `;
        } catch (tableError) {
            console.log('检查表结构失败，使用基础查询:', tableError.message);
            // 如果检查表结构失败，使用最基础的查询
            sql = `
                SELECT
                    s.device_id,
                    s.device_id as device_name,
                    'offline' as status,
                    MAX(s.updated_at) as last_active,
                    SUM(CASE WHEN s.task_type = 'like' THEN s.total_count ELSE 0 END) as like_count,
                    SUM(CASE WHEN s.task_type = 'collect' THEN s.total_count ELSE 0 END) as collect_count,
                    SUM(CASE WHEN s.task_type = 'follow' THEN s.total_count ELSE 0 END) as follow_count,
                    SUM(CASE WHEN s.task_type = 'comment' THEN s.total_count ELSE 0 END) as comment_count,
                    SUM(CASE WHEN s.task_type = 'message' THEN s.total_count ELSE 0 END) as message_count,
                    SUM(CASE WHEN s.task_type = 'recommend' THEN s.total_count ELSE 0 END) as recommend_count,
                    SUM(CASE WHEN s.task_type = 'comment_like' THEN s.total_count ELSE 0 END) as comment_like_count,
                    SUM(CASE WHEN s.task_type = 'comment_reply' THEN s.total_count ELSE 0 END) as comment_reply_count,
                    0 as commentLike_count,
                    0 as commentReply_count,
                    0 as commentUserLike_count,
                    0 as commentUserRecommend_count,
                    0 as commentUserCollect_count,
                    0 as commentUserMessage_count
                FROM device_task_stats s
                ${whereClause.replace('device_id', 's.device_id')}
                GROUP BY s.device_id
                ORDER BY MAX(s.updated_at) DESC
                LIMIT ? OFFSET ?
            `;
        }

        const offset = (page - 1) * limit;
        params.push(parseInt(limit), parseInt(offset));

        const [rows] = await connection.query(sql, params);

        // 获取总数
        const countSql = `
            SELECT COUNT(DISTINCT s.device_id) as total
            FROM device_task_stats s
            LEFT JOIN device_status ds ON s.device_id = ds.device_id
            ${whereClause.replace('device_id', 's.device_id').replace('status', 'ds.status')}
        `;
        const [countResult] = await connection.query(countSql, params.slice(0, -2));

        connection.release();

        res.json({
            code: 0,
            data: rows,
            count: countResult[0]?.total || 0
        });

    } catch (error) {
        console.error('获取设备任务统计失败:', error);
        res.json({
            code: -1,
            msg: '获取设备任务统计失败: ' + error.message,
            data: []
        });
    }
});

// 获取设备详细统计
router.get('/getDeviceDetailStats', async (req, res) => {
    try {
        const { device_id } = req.query;

        if (!device_id) {
            return res.json({
                code: -1,
                msg: '设备ID不能为空'
            });
        }

        const connection = await pool.getConnection();

        // 查询设备的详细任务记录
        const sql = `
            SELECT
                execution_data,
                status,
                created_at
            FROM device_logs
            WHERE device_id = ?
            ORDER BY created_at DESC
            LIMIT 100
        `;

        const [rows] = await connection.query(sql, [device_id]);
        connection.release();

        // 分析数据并统计各类任务
        const stats = {
            like: { total: 0, success: 0, failed: 0, last_time: null },
            collect: { total: 0, success: 0, failed: 0, last_time: null },
            follow: { total: 0, success: 0, failed: 0, last_time: null },
            comment: { total: 0, success: 0, failed: 0, last_time: null },
            message: { total: 0, success: 0, failed: 0, last_time: null },
            recommend: { total: 0, success: 0, failed: 0, last_time: null },
            comment_like: { total: 0, success: 0, failed: 0, last_time: null },
            comment_reply: { total: 0, success: 0, failed: 0, last_time: null }
        };

        rows.forEach(row => {
            const data = row.execution_data || '';
            const status = row.status;
            const time = row.created_at;

            // 根据execution_data内容判断任务类型
            Object.keys(stats).forEach(taskType => {
                if (data.includes(taskType)) {
                    stats[taskType].total++;
                    if (status === 'completed') {
                        stats[taskType].success++;
                    } else if (status === 'failed') {
                        stats[taskType].failed++;
                    }
                    if (!stats[taskType].last_time || time > stats[taskType].last_time) {
                        stats[taskType].last_time = time;
                    }
                }
            });
        });

        res.json({
            code: 0,
            data: stats
        });
    } catch (error) {
        console.error('获取设备详细统计失败:', error);
        res.json({
            code: -1,
            msg: '获取设备详细统计失败: ' + error.message
        });
    }
});

// 重置设备统计
router.post('/resetDeviceStats', async (req, res) => {
    try {
        const { device_id } = req.body;

        if (!device_id) {
            return res.json({
                code: -1,
                msg: '设备ID不能为空'
            });
        }

        const connection = await pool.getConnection();

        // 删除该设备的所有日志记录
        const sql = 'DELETE FROM device_logs WHERE device_id = ?';
        await connection.query(sql, [device_id]);

        connection.release();

        res.json({
            code: 0,
            msg: '重置成功'
        });
    } catch (error) {
        console.error('重置设备统计失败:', error);
        res.json({
            code: -1,
            msg: '重置设备统计失败: ' + error.message
        });
    }
});

// 处理设备任务数据上报（通过WebSocket）
async function handleDeviceTaskReport(data) {
    try {
        const { device_id, task_type, success, execution_data } = data;

        if (!pool) {
            console.error('MySQL连接池未初始化');
            return;
        }

        const connection = await pool.getConnection();

        // 直接记录到设备日志表
        const sql = `
            INSERT INTO device_logs (device_id, task_type, status, execution_data)
            VALUES (?, ?, ?, ?)
        `;

        const status = success ? 'completed' : 'failed';
        const logData = JSON.stringify({
            task_type,
            execution_data,
            timestamp: new Date().toISOString()
        });

        await connection.query(sql, [device_id, task_type, status, logData]);
        connection.release();

        console.log(`设备任务记录成功: ${device_id} - ${task_type} - ${status}`);

    } catch (error) {
        console.error('处理设备任务上报失败:', error);
    }
}

// 任务监控相关接口

// 获取任务状态
router.get('/getTaskStatus', async (req, res) => {
    try {
        if (!pool) {
            return res.json({
                code: -1,
                msg: 'MySQL连接池未初始化'
            });
        }

        const connection = await pool.getConnection();

        // 查询运行中的任务和卡住的任务
        const sql = `
            SELECT
                t.id as task_id,
                t.created_at,
                t.group_ids,
                COUNT(tq.id) as total_devices,
                COUNT(CASE WHEN tq.status = 'completed' THEN 1 END) as completed_devices,
                COUNT(CASE WHEN tq.status = 'running' AND tq.updated_at < DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN 1 END) as stuck_count,
                GROUP_CONCAT(CASE WHEN tq.status = 'running' AND tq.updated_at < DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN tq.device_id END) as stuck_devices
            FROM tasks t
            LEFT JOIN task_queue tq ON t.id = tq.task_id
            WHERE t.status IN ('running', 'pending')
            GROUP BY t.id, t.created_at, t.group_ids
            HAVING total_devices > 0
            ORDER BY t.created_at DESC
        `;

        const [rows] = await connection.query(sql);
        connection.release();

        // 处理结果
        const taskStatus = rows.map(row => ({
            task_id: row.task_id,
            created_at: row.created_at,
            group_ids: row.group_ids,
            total_devices: row.total_devices,
            completed_devices: row.completed_devices,
            stuck: row.stuck_count > 0,
            stuck_devices: row.stuck_devices ? row.stuck_devices.split(',') : []
        }));

        res.json({
            code: 0,
            data: taskStatus
        });

    } catch (error) {
        console.error('获取任务状态失败:', error);
        res.json({
            code: -1,
            msg: '获取任务状态失败: ' + error.message
        });
    }
});

// 强制完成卡住的任务
router.post('/forceCompleteStuckTasks', async (req, res) => {
    try {
        if (!pool) {
            return res.json({
                code: -1,
                msg: 'MySQL连接池未初始化'
            });
        }

        const connection = await pool.getConnection();

        // 查找卡住的任务队列记录（5分钟未更新的running状态）
        const updateSql = `
            UPDATE task_queue
            SET status = 'completed',
                completed_at = NOW(),
                updated_at = NOW()
            WHERE status = 'running'
            AND updated_at < DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        `;

        const [result] = await connection.query(updateSql);

        // 检查是否有任务可以标记为完成
        const checkTasksSql = `
            UPDATE tasks t
            SET status = 'completed'
            WHERE t.status = 'running'
            AND NOT EXISTS (
                SELECT 1 FROM task_queue tq
                WHERE tq.task_id = t.id
                AND tq.status IN ('pending', 'running')
            )
        `;

        await connection.query(checkTasksSql);
        connection.release();

        res.json({
            code: 0,
            msg: `已强制完成 ${result.affectedRows} 个卡住的设备任务`
        });

    } catch (error) {
        console.error('强制完成任务失败:', error);
        res.json({
            code: -1,
            msg: '强制完成任务失败: ' + error.message
        });
    }
});

// 取消卡住的任务
router.post('/cancelStuckTasks', async (req, res) => {
    try {
        if (!pool) {
            return res.json({
                code: -1,
                msg: 'MySQL连接池未初始化'
            });
        }

        const connection = await pool.getConnection();

        // 删除卡住的任务队列记录
        const deleteSql = `
            DELETE FROM task_queue
            WHERE status = 'running'
            AND updated_at < DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        `;

        const [result] = await connection.query(deleteSql);

        // 检查是否有任务需要取消
        const cancelTasksSql = `
            UPDATE tasks t
            SET status = 'cancelled'
            WHERE t.status = 'running'
            AND NOT EXISTS (
                SELECT 1 FROM task_queue tq
                WHERE tq.task_id = t.id
                AND tq.status IN ('pending', 'running', 'completed')
            )
        `;

        await connection.query(cancelTasksSql);
        connection.release();

        res.json({
            code: 0,
            msg: `已取消 ${result.affectedRows} 个卡住的设备任务`
        });

    } catch (error) {
        console.error('取消任务失败:', error);
        res.json({
            code: -1,
            msg: '取消任务失败: ' + error.message
        });
    }
});

// 刷新设备状态
router.post('/refreshDeviceStatus', async (req, res) => {
    try {
        // 这里可以添加刷新设备状态的逻辑
        // 比如检查WebSocket连接状态，更新设备在线状态等

        res.json({
            code: 0,
            msg: '设备状态已刷新'
        });

    } catch (error) {
        console.error('刷新设备状态失败:', error);
        res.json({
            code: -1,
            msg: '刷新设备状态失败: ' + error.message
        });
    }
});

// 紧急修复卡住任务
router.post('/emergencyFixStuckTasks', async (req, res) => {
    try {
        if (!pool) {
            return res.json({
                code: -1,
                msg: 'MySQL连接池未初始化'
            });
        }

        const connection = await pool.getConnection();

        // 1. 查找所有卡住的任务
        const findStuckSql = `
            SELECT DISTINCT t.id as task_id, t.group_ids
            FROM tasks t
            LEFT JOIN task_queue tq ON t.id = tq.task_id
            WHERE t.status = 'running'
            AND (
                tq.status = 'pending'
                OR (tq.status = 'running' AND tq.updated_at < DATE_SUB(NOW(), INTERVAL 3 MINUTE))
            )
            GROUP BY t.id, t.group_ids
        `;

        const [stuckTasks] = await connection.query(findStuckSql);

        let fixedCount = 0;

        for (const task of stuckTasks) {
            // 2. 强制完成所有pending和长时间running的任务
            const forceCompleteSql = `
                UPDATE task_queue
                SET status = 'completed',
                    completed_at = NOW(),
                    updated_at = NOW()
                WHERE task_id = ?
                AND (
                    status = 'pending'
                    OR (status = 'running' AND updated_at < DATE_SUB(NOW(), INTERVAL 3 MINUTE))
                )
            `;

            const [result] = await connection.query(forceCompleteSql, [task.task_id]);
            fixedCount += result.affectedRows;

            // 3. 检查任务是否可以标记为完成
            const checkCompleteSql = `
                UPDATE tasks
                SET status = 'completed'
                WHERE id = ?
                AND NOT EXISTS (
                    SELECT 1 FROM task_queue
                    WHERE task_id = ?
                    AND status IN ('pending', 'running')
                )
            `;

            await connection.query(checkCompleteSql, [task.task_id, task.task_id]);
        }

        connection.release();

        res.json({
            code: 0,
            msg: `紧急修复完成！处理了 ${stuckTasks.length} 个卡住任务，修复了 ${fixedCount} 个设备任务`
        });

    } catch (error) {
        console.error('紧急修复失败:', error);
        res.json({
            code: -1,
            msg: '紧急修复失败: ' + error.message
        });
    }
});

// 多并发任务数据接收接口
router.post('/batchTaskData', async (req, res) => {
    try {
        const { tasks } = req.body;

        if (!tasks || !Array.isArray(tasks)) {
            return res.json({
                code: -1,
                msg: '任务数据格式错误，需要tasks数组'
            });
        }

        if (!pool) {
            return res.json({
                code: -1,
                msg: 'MySQL连接池未初始化'
            });
        }

        const connection = await pool.getConnection();

        try {
            // 开始事务
            await connection.beginTransaction();

            let successCount = 0;
            let failedCount = 0;
            const results = [];

            for (const taskData of tasks) {
                try {
                    const {
                        device_id,
                        device_name,
                        task_type,
                        success,
                        execution_data,
                        timestamp
                    } = taskData;

                    // 验证必要字段
                    if (!device_id || !task_type) {
                        results.push({
                            device_id: device_id || 'unknown',
                            success: false,
                            error: '缺少必要字段device_id或task_type'
                        });
                        failedCount++;
                        continue;
                    }

                    // 插入设备日志
                    const logSql = `
                        INSERT INTO device_logs (device_id, device_name, task_type, status, execution_data, created_at)
                        VALUES (?, ?, ?, ?, ?, ?)
                    `;

                    const status = success ? 'completed' : 'failed';
                    const logTime = timestamp ? new Date(timestamp) : new Date();

                    await connection.query(logSql, [
                        device_id,
                        device_name || device_id,
                        task_type,
                        status,
                        JSON.stringify(execution_data || {}),
                        logTime
                    ]);

                    // 更新设备任务统计
                    const statsSql = `
                        INSERT INTO device_task_stats (device_id, device_name, task_type, success_count, failed_count, total_count, last_execution_time)
                        VALUES (?, ?, ?, ?, ?, 1, ?)
                        ON DUPLICATE KEY UPDATE
                            success_count = success_count + ?,
                            failed_count = failed_count + ?,
                            total_count = total_count + 1,
                            last_execution_time = ?,
                            device_name = COALESCE(VALUES(device_name), device_name)
                    `;

                    const successIncrement = success ? 1 : 0;
                    const failedIncrement = success ? 0 : 1;

                    await connection.query(statsSql, [
                        device_id,
                        device_name || device_id,
                        task_type,
                        successIncrement,
                        failedIncrement,
                        logTime,
                        successIncrement,
                        failedIncrement,
                        logTime
                    ]);

                    // 更新设备状态
                    const deviceStatusSql = `
                        INSERT INTO device_status (device_id, device_name, status, last_heartbeat)
                        VALUES (?, ?, 'online', ?)
                        ON DUPLICATE KEY UPDATE
                            device_name = COALESCE(VALUES(device_name), device_name),
                            status = 'online',
                            last_heartbeat = ?
                    `;

                    await connection.query(deviceStatusSql, [
                        device_id,
                        device_name || device_id,
                        logTime,
                        logTime
                    ]);

                    results.push({
                        device_id: device_id,
                        success: true,
                        task_type: task_type
                    });
                    successCount++;

                } catch (taskError) {
                    console.error(`处理任务失败 (设备: ${taskData.device_id}):`, taskError);
                    results.push({
                        device_id: taskData.device_id || 'unknown',
                        success: false,
                        error: taskError.message
                    });
                    failedCount++;
                }
            }

            // 提交事务
            await connection.commit();

            res.json({
                code: 0,
                msg: `批量处理完成: 成功${successCount}个，失败${failedCount}个`,
                data: {
                    total: tasks.length,
                    success: successCount,
                    failed: failedCount,
                    results: results
                }
            });

        } catch (error) {
            // 回滚事务
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }

    } catch (error) {
        console.error('批量任务数据处理失败:', error);
        res.json({
            code: -1,
            msg: '批量处理失败: ' + error.message
        });
    }
});

// 单个任务数据接收接口（兼容现有系统）
router.post('/taskData', async (req, res) => {
    try {
        const {
            device_id,
            device_name,
            task_type,
            success,
            execution_data,
            timestamp
        } = req.body;

        // 验证必要字段
        if (!device_id || !task_type) {
            return res.json({
                code: -1,
                msg: '缺少必要字段device_id或task_type'
            });
        }

        if (!pool) {
            return res.json({
                code: -1,
                msg: 'MySQL连接池未初始化'
            });
        }

        const connection = await pool.getConnection();

        try {
            // 插入设备日志
            const logSql = `
                INSERT INTO device_logs (device_id, device_name, task_type, status, execution_data, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            `;

            const status = success ? 'completed' : 'failed';
            const logTime = timestamp ? new Date(timestamp) : new Date();

            await connection.query(logSql, [
                device_id,
                device_name || device_id,
                task_type,
                status,
                JSON.stringify(execution_data || {}),
                logTime
            ]);

            // 更新设备任务统计
            const statsSql = `
                INSERT INTO device_task_stats (device_id, device_name, task_type, success_count, failed_count, total_count, last_execution_time)
                VALUES (?, ?, ?, ?, ?, 1, ?)
                ON DUPLICATE KEY UPDATE
                    success_count = success_count + ?,
                    failed_count = failed_count + ?,
                    total_count = total_count + 1,
                    last_execution_time = ?,
                    device_name = COALESCE(VALUES(device_name), device_name)
            `;

            const successIncrement = success ? 1 : 0;
            const failedIncrement = success ? 0 : 1;

            await connection.query(statsSql, [
                device_id,
                device_name || device_id,
                task_type,
                successIncrement,
                failedIncrement,
                logTime,
                successIncrement,
                failedIncrement,
                logTime
            ]);

            // 更新设备状态
            const deviceStatusSql = `
                INSERT INTO device_status (device_id, device_name, status, last_heartbeat)
                VALUES (?, ?, 'online', ?)
                ON DUPLICATE KEY UPDATE
                    device_name = COALESCE(VALUES(device_name), device_name),
                    status = 'online',
                    last_heartbeat = ?
            `;

            await connection.query(deviceStatusSql, [
                device_id,
                device_name || device_id,
                logTime,
                logTime
            ]);

            res.json({
                code: 0,
                msg: '任务数据记录成功',
                data: {
                    device_id: device_id,
                    task_type: task_type,
                    status: status
                }
            });

        } finally {
            connection.release();
        }

    } catch (error) {
        console.error('任务数据处理失败:', error);
        res.json({
            code: -1,
            msg: '处理失败: ' + error.message
        });
    }
});

// ==================== 设备数据上报接口 ====================

// 设备认证和数据上报接口
router.post('/device/report', async (req, res) => {
    try {
        const {
            device_name,        // 设备名称（必填）
            user_name,          // 用户名（必填）
            device_uuid,        // 设备UUID（可选）
            task_type,          // 任务类型（必填）
            success,            // 是否成功（必填）
            execution_data,     // 执行数据（可选）
            timestamp           // 时间戳（可选）
        } = req.body;

        // 1. 参数验证
        if (!device_name || !user_name || !task_type || success === undefined) {
            return res.json({
                code: -1,
                msg: '缺少必要参数: device_name, user_name, task_type, success'
            });
        }

        // 2. 验证用户是否存在
        const userDoc = await userMod.findOne({ userName: user_name });
        if (!userDoc) {
            return res.json({
                code: -1,
                msg: `用户 ${user_name} 不存在`
            });
        }

        // 3. 验证设备是否属于该用户
        const deviceMod = mongoose.model(`device_${user_name}`, deviceShe);
        let deviceDoc = await deviceMod.findOne({ deviceName: device_name });

        if (!deviceDoc) {
            // 如果设备不存在，自动注册设备
            console.log(`设备 ${device_name} 不存在，自动注册到用户 ${user_name}`);

            const newDevice = {
                deviceName: device_name,
                device_UUID: device_uuid || '',
                taskStatus: '空闲',
                deviceMsg: '设备自动注册',
                taskName: '无任务',
                updateTime: new Date().toLocaleString(),
                assignedUser: userDoc._id
            };

            deviceDoc = await deviceMod.create(newDevice);

            // 同时在主设备集合中创建记录
            const mainDeviceMod = mongoose.model('device_cjroot', deviceShe);
            await mainDeviceMod.updateOne(
                { deviceName: device_name },
                {
                    ...newDevice,
                    assignedUsers: [userDoc._id]
                },
                { upsert: true }
            );
        }

        // 4. 记录任务数据到MySQL
        if (pool) {
            const connection = await pool.getConnection();

            try {
                // 插入设备日志
                const logSql = `
                    INSERT INTO device_logs (device_id, device_name, task_type, status, execution_data, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                `;

                const status = success ? 'completed' : 'failed';
                const logTime = timestamp ? new Date(timestamp) : new Date();

                await connection.query(logSql, [
                    device_name,
                    device_name,
                    task_type,
                    status,
                    JSON.stringify(execution_data || {}),
                    logTime
                ]);

                // 更新设备任务统计
                const statsSql = `
                    INSERT INTO device_task_stats (device_id, device_name, task_type, success_count, failed_count, total_count, last_execution_time)
                    VALUES (?, ?, ?, ?, ?, 1, ?)
                    ON DUPLICATE KEY UPDATE
                        success_count = success_count + ?,
                        failed_count = failed_count + ?,
                        total_count = total_count + 1,
                        last_execution_time = ?,
                        device_name = COALESCE(VALUES(device_name), device_name)
                `;

                const successIncrement = success ? 1 : 0;
                const failedIncrement = success ? 0 : 1;

                await connection.query(statsSql, [
                    device_name,
                    device_name,
                    task_type,
                    successIncrement,
                    failedIncrement,
                    logTime,
                    successIncrement,
                    failedIncrement,
                    logTime
                ]);

                // 更新设备状态
                const deviceStatusSql = `
                    INSERT INTO device_status (device_id, device_name, status, last_heartbeat)
                    VALUES (?, ?, 'online', ?)
                    ON DUPLICATE KEY UPDATE
                        device_name = COALESCE(VALUES(device_name), device_name),
                        status = 'online',
                        last_heartbeat = ?
                `;

                await connection.query(deviceStatusSql, [
                    device_name,
                    device_name,
                    logTime,
                    logTime
                ]);

            } finally {
                connection.release();
            }
        }

        // 5. 更新MongoDB中的设备状态
        await deviceMod.updateOne(
            { deviceName: device_name },
            {
                taskStatus: '忙碌',
                deviceMsg: `执行${task_type}任务${success ? '成功' : '失败'}`,
                updateTime: new Date().toLocaleString()
            }
        );

        res.json({
            code: 0,
            msg: '数据上报成功',
            data: {
                device_name: device_name,
                user_name: user_name,
                task_type: task_type,
                status: success ? 'completed' : 'failed',
                timestamp: new Date().toISOString()
            }
        });

    } catch (error) {
        console.error('设备数据上报失败:', error);
        res.json({
            code: -1,
            msg: '数据上报失败: ' + error.message
        });
    }
});

// 设备统计数据直接上传接口 ⭐ (推荐用于批量统计)
router.post('/device/uploadStats', async (req, res) => {
    try {
        const {
            device_name,        // 设备名称（必填）
            user_name,          // 用户名（必填）
            stats               // 统计数据对象（必填）
        } = req.body;

        // 1. 参数验证
        if (!device_name || !user_name || !stats) {
            return res.json({
                code: -1,
                msg: '缺少必要参数: device_name, user_name, stats'
            });
        }

        // 2. 验证用户是否存在
        const userDoc = await userMod.findOne({ userName: user_name });
        if (!userDoc) {
            return res.json({
                code: -1,
                msg: `用户 ${user_name} 不存在`
            });
        }

        if (!pool) {
            return res.json({
                code: -1,
                msg: 'MySQL连接池未初始化'
            });
        }

        const connection = await pool.getConnection();

        try {
            // 开始事务
            await connection.beginTransaction();

            const logTime = new Date();
            let updatedTasks = [];

            // 支持的任务类型 - 包含新的评论区操作类型
            const taskTypes = [
                'like', 'collect', 'follow', 'comment', 'message', 'recommend',
                'comment_like', 'comment_reply',
                // 新增的评论区操作类型
                'commentLike', 'commentReply', 'commentUserLike',
                'commentUserRecommend', 'commentUserCollect', 'commentUserMessage'
            ];

            for (const taskType of taskTypes) {
                if (stats[taskType] && stats[taskType] > 0) {
                    const count = parseInt(stats[taskType]);

                    try {
                        // 先检查表结构，确定可用字段
                        const [columns] = await connection.query('DESCRIBE device_task_stats');
                        const hasDeviceName = columns.some(col => col.Field === 'device_name');

                        let statsSql, params;

                        if (hasDeviceName) {
                            // 如果有device_name字段，使用完整版本
                            statsSql = `
                                INSERT INTO device_task_stats (device_id, device_name, task_type, success_count, failed_count, total_count, last_execution_time)
                                VALUES (?, ?, ?, ?, 0, ?, ?)
                                ON DUPLICATE KEY UPDATE
                                    success_count = success_count + VALUES(success_count),
                                    total_count = total_count + VALUES(total_count),
                                    last_execution_time = VALUES(last_execution_time),
                                    device_name = COALESCE(VALUES(device_name), device_name)
                            `;
                            params = [device_name, device_name, taskType, count, count, logTime];
                        } else {
                            // 如果没有device_name字段，使用简化版本
                            statsSql = `
                                INSERT INTO device_task_stats (device_id, task_type, success_count, failed_count, total_count, last_execution_time)
                                VALUES (?, ?, ?, 0, ?, ?)
                                ON DUPLICATE KEY UPDATE
                                    success_count = success_count + VALUES(success_count),
                                    total_count = total_count + VALUES(total_count),
                                    last_execution_time = VALUES(last_execution_time)
                            `;
                            params = [device_name, taskType, count, count, logTime];
                        }

                        await connection.query(statsSql, params);

                    } catch (insertError) {
                        console.error(`插入任务类型 ${taskType} 失败:`, insertError.message);
                        // 如果插入失败，尝试最基础的插入方式
                        try {
                            const basicSql = `
                                INSERT INTO device_task_stats (device_id, task_type, total_count)
                                VALUES (?, ?, ?)
                                ON DUPLICATE KEY UPDATE total_count = total_count + VALUES(total_count)
                            `;
                            await connection.query(basicSql, [device_name, taskType, count]);
                            console.log(`使用基础方式插入 ${taskType} 成功`);
                        } catch (basicError) {
                            console.error(`基础插入也失败:`, basicError.message);
                            continue; // 跳过这个任务类型
                        }
                    }

                    updatedTasks.push({
                        task_type: taskType,
                        count: count
                    });
                }
            }

            // 更新设备状态 - 兼容版本
            try {
                await connection.query(`
                    INSERT INTO device_status (device_id, status, last_heartbeat)
                    VALUES (?, 'online', NOW())
                    ON DUPLICATE KEY UPDATE
                        status = 'online',
                        last_heartbeat = NOW()
                `, [device_name]);
            } catch (statusError) {
                // 如果device_status表不存在，忽略错误
                console.log('设备状态更新失败（可能表不存在）:', statusError.message);
            }

            // 提交事务
            await connection.commit();

            res.json({
                code: 0,
                msg: '统计数据上传成功',
                data: {
                    device_name: device_name,
                    user_name: user_name,
                    updated_tasks: updatedTasks,
                    timestamp: new Date().toISOString()
                }
            });

        } catch (error) {
            // 回滚事务
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }

    } catch (error) {
        console.error('统计数据上传失败:', error);
        res.json({
            code: -1,
            msg: '统计数据上传失败: ' + error.message
        });
    }
});

// 设备批量数据上报接口
router.post('/device/batchReport', async (req, res) => {
    try {
        const { user_name, reports } = req.body;

        if (!user_name || !reports || !Array.isArray(reports)) {
            return res.json({
                code: -1,
                msg: '参数错误: 需要user_name和reports数组'
            });
        }

        // 验证用户是否存在
        const userDoc = await userMod.findOne({ userName: user_name });
        if (!userDoc) {
            return res.json({
                code: -1,
                msg: `用户 ${user_name} 不存在`
            });
        }

        const deviceMod = mongoose.model(`device_${user_name}`, deviceShe);
        const results = [];
        let successCount = 0;
        let failedCount = 0;

        // 处理每个设备的数据上报
        for (const report of reports) {
            try {
                const {
                    device_name,
                    task_type,
                    success,
                    execution_data,
                    timestamp
                } = report;

                if (!device_name || !task_type || success === undefined) {
                    results.push({
                        device_name: device_name || 'unknown',
                        success: false,
                        error: '缺少必要参数'
                    });
                    failedCount++;
                    continue;
                }

                // 验证设备是否存在，不存在则自动注册
                let deviceDoc = await deviceMod.findOne({ deviceName: device_name });
                if (!deviceDoc) {
                    const newDevice = {
                        deviceName: device_name,
                        taskStatus: '空闲',
                        deviceMsg: '设备自动注册',
                        taskName: '无任务',
                        updateTime: new Date().toLocaleString(),
                        assignedUser: userDoc._id
                    };

                    deviceDoc = await deviceMod.create(newDevice);
                }

                // 记录到MySQL（如果连接池可用）
                if (pool) {
                    const connection = await pool.getConnection();

                    try {
                        const status = success ? 'completed' : 'failed';
                        const logTime = timestamp ? new Date(timestamp) : new Date();

                        // 插入日志
                        await connection.query(`
                            INSERT INTO device_logs (device_id, device_name, task_type, status, execution_data, created_at)
                            VALUES (?, ?, ?, ?, ?, ?)
                        `, [device_name, device_name, task_type, status, JSON.stringify(execution_data || {}), logTime]);

                        // 更新统计
                        const successIncrement = success ? 1 : 0;
                        const failedIncrement = success ? 0 : 1;

                        await connection.query(`
                            INSERT INTO device_task_stats (device_id, device_name, task_type, success_count, failed_count, total_count, last_execution_time)
                            VALUES (?, ?, ?, ?, ?, 1, ?)
                            ON DUPLICATE KEY UPDATE
                                success_count = success_count + ?,
                                failed_count = failed_count + ?,
                                total_count = total_count + 1,
                                last_execution_time = ?
                        `, [device_name, device_name, task_type, successIncrement, failedIncrement, logTime, successIncrement, failedIncrement, logTime]);

                    } finally {
                        connection.release();
                    }
                }

                // 更新设备状态
                await deviceMod.updateOne(
                    { deviceName: device_name },
                    {
                        taskStatus: '忙碌',
                        deviceMsg: `执行${task_type}任务${success ? '成功' : '失败'}`,
                        updateTime: new Date().toLocaleString()
                    }
                );

                results.push({
                    device_name: device_name,
                    task_type: task_type,
                    success: true
                });
                successCount++;

            } catch (error) {
                console.error(`处理设备 ${report.device_name} 数据失败:`, error);
                results.push({
                    device_name: report.device_name || 'unknown',
                    success: false,
                    error: error.message
                });
                failedCount++;
            }
        }

        res.json({
            code: 0,
            msg: `批量上报完成: 成功${successCount}个，失败${failedCount}个`,
            data: {
                user_name: user_name,
                total: reports.length,
                success: successCount,
                failed: failedCount,
                results: results
            }
        });

    } catch (error) {
        console.error('批量设备数据上报失败:', error);
        res.json({
            code: -1,
            msg: '批量上报失败: ' + error.message
        });
    }
});

// 设备注册接口
router.post('/device/register', async (req, res) => {
    try {
        const {
            device_name,
            user_name,
            device_uuid,
            device_width,
            device_height
        } = req.body;

        if (!device_name || !user_name) {
            return res.json({
                code: -1,
                msg: '缺少必要参数: device_name, user_name'
            });
        }

        // 验证用户是否存在
        const userDoc = await userMod.findOne({ userName: user_name });
        if (!userDoc) {
            return res.json({
                code: -1,
                msg: `用户 ${user_name} 不存在`
            });
        }

        const deviceMod = mongoose.model(`device_${user_name}`, deviceShe);

        // 检查设备是否已存在
        const existingDevice = await deviceMod.findOne({ deviceName: device_name });
        if (existingDevice) {
            return res.json({
                code: -1,
                msg: `设备 ${device_name} 已存在`
            });
        }

        // 创建新设备
        const newDevice = {
            deviceName: device_name,
            device_UUID: device_uuid || '',
            device_width: device_width || 0,
            device_height: device_height || 0,
            taskStatus: '空闲',
            deviceMsg: '设备注册成功',
            taskName: '无任务',
            updateTime: new Date().toLocaleString(),
            assignedUser: userDoc._id
        };

        const deviceDoc = await deviceMod.create(newDevice);

        // 同时在主设备集合中创建记录
        const mainDeviceMod = mongoose.model('device_cjroot', deviceShe);
        await mainDeviceMod.updateOne(
            { deviceName: device_name },
            {
                ...newDevice,
                assignedUsers: [userDoc._id]
            },
            { upsert: true }
        );

        res.json({
            code: 0,
            msg: '设备注册成功',
            data: {
                device_id: deviceDoc._id,
                device_name: device_name,
                user_name: user_name,
                status: 'registered'
            }
        });

    } catch (error) {
        console.error('设备注册失败:', error);
        res.json({
            code: -1,
            msg: '设备注册失败: ' + error.message
        });
    }
});

// 设备心跳接口
router.post('/device/heartbeat', async (req, res) => {
    try {
        const { device_name, user_name, status } = req.body;

        if (!device_name || !user_name) {
            return res.json({
                code: -1,
                msg: '缺少必要参数: device_name, user_name'
            });
        }

        // 验证用户和设备
        const userDoc = await userMod.findOne({ userName: user_name });
        if (!userDoc) {
            return res.json({
                code: -1,
                msg: `用户 ${user_name} 不存在`
            });
        }

        const deviceMod = mongoose.model(`device_${user_name}`, deviceShe);
        const deviceDoc = await deviceMod.findOne({ deviceName: device_name });

        if (!deviceDoc) {
            return res.json({
                code: -1,
                msg: `设备 ${device_name} 不存在`
            });
        }

        // 更新设备心跳
        await deviceMod.updateOne(
            { deviceName: device_name },
            {
                taskStatus: status || '空闲',
                updateTime: new Date().toLocaleString()
            }
        );

        // 更新MySQL设备状态
        if (pool) {
            const connection = await pool.getConnection();
            try {
                await connection.query(`
                    INSERT INTO device_status (device_id, device_name, status, last_heartbeat)
                    VALUES (?, ?, 'online', NOW())
                    ON DUPLICATE KEY UPDATE
                        status = 'online',
                        last_heartbeat = NOW()
                `, [device_name, device_name]);
            } finally {
                connection.release();
            }
        }

        res.json({
            code: 0,
            msg: '心跳更新成功',
            data: {
                device_name: device_name,
                status: status || '空闲',
                timestamp: new Date().toISOString()
            }
        });

    } catch (error) {
        console.error('设备心跳更新失败:', error);
        res.json({
            code: -1,
            msg: '心跳更新失败: ' + error.message
        });
    }
});

// 获取任务汇总统计
async function getTaskSummary(res) {
    try {
        if (!pool) {
            return res.json({
                code: -1,
                msg: 'MySQL连接池未初始化'
            });
        }

        const connection = await pool.getConnection();

        // 查询各类任务的汇总统计
        const sql = `
            SELECT
                task_type,
                SUM(success_count) as success_count,
                SUM(failed_count) as failed_count,
                SUM(total_count) as total_count,
                COUNT(DISTINCT device_id) as device_count,
                MAX(last_execution_time) as last_execution_time
            FROM device_task_stats
            GROUP BY task_type
        `;

        const [rows] = await connection.query(sql);
        connection.release();

        // 构建汇总数据
        const summary = {
            like_count: 0,
            collect_count: 0,
            follow_count: 0,
            comment_count: 0,
            message_count: 0,
            recommend_count: 0,
            comment_like_count: 0,
            comment_reply_count: 0,
            // 新增的评论区操作统计
            commentLike_count: 0,
            commentReply_count: 0,
            commentUserLike_count: 0,
            commentUserRecommend_count: 0,
            commentUserCollect_count: 0,
            commentUserMessage_count: 0,
            total_devices: 0,
            total_tasks: 0
        };

        const deviceSet = new Set();

        rows.forEach(row => {
            const taskType = row.task_type;
            const totalCount = row.total_count || 0;

            // 统计设备数量
            if (row.device_count) {
                deviceSet.add(row.device_count);
            }

            // 根据任务类型累加统计
            switch (taskType) {
                case 'like':
                    summary.like_count += totalCount;
                    break;
                case 'collect':
                    summary.collect_count += totalCount;
                    break;
                case 'follow':
                    summary.follow_count += totalCount;
                    break;
                case 'comment':
                    summary.comment_count += totalCount;
                    break;
                case 'message':
                    summary.message_count += totalCount;
                    break;
                case 'recommend':
                    summary.recommend_count += totalCount;
                    break;
                case 'comment_like':
                    summary.comment_like_count += totalCount;
                    break;
                case 'comment_reply':
                    summary.comment_reply_count += totalCount;
                    break;
                // 新增的评论区操作类型
                case 'commentLike':
                    summary.commentLike_count += totalCount;
                    break;
                case 'commentReply':
                    summary.commentReply_count += totalCount;
                    break;
                case 'commentUserLike':
                    summary.commentUserLike_count += totalCount;
                    break;
                case 'commentUserRecommend':
                    summary.commentUserRecommend_count += totalCount;
                    break;
                case 'commentUserCollect':
                    summary.commentUserCollect_count += totalCount;
                    break;
                case 'commentUserMessage':
                    summary.commentUserMessage_count += totalCount;
                    break;
            }

            summary.total_tasks += totalCount;
        });

        // 计算总设备数
        summary.total_devices = deviceSet.size;

        res.json({
            code: 0,
            summary: summary,
            details: rows
        });

    } catch (error) {
        console.error('获取任务汇总统计失败:', error);
        res.json({
            code: -1,
            msg: '获取汇总统计失败: ' + error.message
        });
    }
}

// 删除设备数据接口
router.delete('/device/delete/:deviceId', async (req, res) => {
    const { deviceId } = req.params;

    if (!deviceId) {
        return res.status(400).json({
            code: -1,
            msg: '设备ID不能为空'
        });
    }

    try {
        const connection = await pool.getConnection();

        try {
            await connection.beginTransaction();

            // 删除设备日志
            await connection.query('DELETE FROM device_logs WHERE device_id = ?', [deviceId]);

            // 删除设备统计
            await connection.query('DELETE FROM device_task_stats WHERE device_id = ?', [deviceId]);

            // 删除设备状态
            await connection.query('DELETE FROM device_status WHERE device_id = ?', [deviceId]);

            // 删除MongoDB中的设备信息
            if (global.db) {
                await global.db.collection('devices').deleteMany({ device_id: deviceId });
            }

            await connection.commit();

            res.json({
                code: 0,
                msg: `设备 ${deviceId} 数据删除成功`
            });

        } catch (error) {
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }

    } catch (error) {
        console.error('删除设备数据失败:', error);
        res.status(500).json({
            code: -1,
            msg: '删除设备数据失败: ' + error.message
        });
    }
});

// 清空所有演示数据接口
router.delete('/device/clearAll', async (req, res) => {
    const { confirm } = req.body;

    if (!confirm) {
        return res.status(400).json({
            code: -1,
            msg: '请确认删除操作'
        });
    }

    try {
        const connection = await pool.getConnection();

        try {
            await connection.beginTransaction();

            // 清空所有表数据
            await connection.query('DELETE FROM device_logs');
            await connection.query('DELETE FROM device_task_stats');
            await connection.query('DELETE FROM device_status');

            // 重置自增ID
            await connection.query('ALTER TABLE device_logs AUTO_INCREMENT = 1');
            await connection.query('ALTER TABLE device_task_stats AUTO_INCREMENT = 1');
            await connection.query('ALTER TABLE device_status AUTO_INCREMENT = 1');

            // 清空MongoDB中的设备信息
            if (global.db) {
                await global.db.collection('devices').deleteMany({});
            }

            await connection.commit();

            res.json({
                code: 0,
                msg: '所有演示数据清理完成'
            });

        } catch (error) {
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }

    } catch (error) {
        console.error('清理数据失败:', error);
        res.status(500).json({
            code: -1,
            msg: '清理数据失败: ' + error.message
        });
    }
});

// 批量删除设备接口
router.delete('/device/batchDelete', async (req, res) => {
    const { deviceIds } = req.body;

    if (!deviceIds || !Array.isArray(deviceIds) || deviceIds.length === 0) {
        return res.status(400).json({
            code: -1,
            msg: '设备ID列表不能为空'
        });
    }

    try {
        const connection = await pool.getConnection();

        try {
            await connection.beginTransaction();

            const placeholders = deviceIds.map(() => '?').join(',');

            // 批量删除设备数据
            await connection.query(`DELETE FROM device_logs WHERE device_id IN (${placeholders})`, deviceIds);
            await connection.query(`DELETE FROM device_task_stats WHERE device_id IN (${placeholders})`, deviceIds);
            await connection.query(`DELETE FROM device_status WHERE device_id IN (${placeholders})`, deviceIds);

            // 删除MongoDB中的设备信息
            if (global.db) {
                await global.db.collection('devices').deleteMany({
                    device_id: { $in: deviceIds }
                });
            }

            await connection.commit();

            res.json({
                code: 0,
                msg: `成功删除 ${deviceIds.length} 个设备的数据`,
                data: {
                    deleted_count: deviceIds.length,
                    device_ids: deviceIds
                }
            });

        } catch (error) {
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }

    } catch (error) {
        console.error('批量删除设备数据失败:', error);
        res.status(500).json({
            code: -1,
            msg: '批量删除失败: ' + error.message
        });
    }
});

// 生成演示数据接口
router.post('/generateDemoData', async (req, res) => {
    try {
        // 使用子进程执行演示数据生成脚本
        const { spawn } = require('child_process');
        const path = require('path');

        const scriptPath = path.join(__dirname, '..', 'create_demo_data.js');
        const child = spawn('node', [scriptPath], {
            cwd: path.join(__dirname, '..')
        });

        child.on('close', (code) => {
            if (code === 0) {
                console.log('演示数据生成完成');
            } else {
                console.error('演示数据生成失败，退出码:', code);
            }
        });

        res.json({
            code: 0,
            msg: '演示数据生成任务已启动'
        });

    } catch (error) {
        console.error('启动演示数据生成失败:', error);
        res.json({
            code: -1,
            msg: '启动失败: ' + error.message
        });
    }
});

module.exports = router;
