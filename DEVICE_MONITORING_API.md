# 设备监控系统 API 文档

## 概述

本文档描述了设备监控系统的多并发任务数据接收接口，专门用于处理来自多个设备的任务执行数据。

## 服务器配置

- **服务器地址**: `*************`
- **应用端口**: `15001`
- **数据库**: `douyin`
- **用户名**: `douyin`
- **密码**: `123456`

## 核心接口

### 1. 批量任务数据接收接口 ⭐

**接口地址**: `POST http://*************:15001/wsRouter/batchTaskData`

**用途**: 高并发批量接收多个设备的任务执行数据

**请求示例**:
```json
{
    "tasks": [
        {
            "device_id": "device_001",
            "device_name": "设备001",
            "task_type": "like",
            "success": true,
            "execution_data": {
                "target_url": "https://example.com/video/123",
                "duration": 1500,
                "additional_info": "点赞成功"
            },
            "timestamp": "2025-01-09T10:30:00Z"
        },
        {
            "device_id": "device_002", 
            "device_name": "设备002",
            "task_type": "collect",
            "success": false,
            "execution_data": {
                "error_message": "网络连接超时",
                "retry_count": 3
            },
            "timestamp": "2025-01-09T10:30:05Z"
        }
    ]
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "批量处理完成: 成功2个，失败0个",
    "data": {
        "total": 2,
        "success": 2,
        "failed": 0,
        "results": [
            {
                "device_id": "device_001",
                "success": true,
                "task_type": "like"
            },
            {
                "device_id": "device_002",
                "success": true,
                "task_type": "collect"
            }
        ]
    }
}
```

### 2. 单个任务数据接收接口

**接口地址**: `POST http://*************:15001/wsRouter/taskData`

**用途**: 接收单个设备的任务执行数据

**请求示例**:
```json
{
    "device_id": "device_001",
    "device_name": "设备001", 
    "task_type": "like",
    "success": true,
    "execution_data": {
        "target_url": "https://example.com/video/123",
        "duration": 1500
    },
    "timestamp": "2025-01-09T10:30:00Z"
}
```

### 3. 设备任务统计查询

**接口地址**: `GET http://*************:15001/wsRouter/getDeviceTaskStats`

**参数**:
- `device_id`: 设备ID (可选)
- `status`: 状态筛选 (可选)
- `page`: 页码，默认1
- `limit`: 每页数量，默认20
- `summary=true`: 获取汇总统计

## 支持的任务类型

| 任务类型 | 说明 | 示例 |
|---------|------|------|
| `like` | 点赞操作 | 视频点赞、评论点赞 |
| `collect` | 收藏操作 | 收藏视频、收藏作品 |
| `follow` | 关注操作 | 关注用户、关注账号 |
| `comment` | 评论操作 | 发表评论、回复评论 |
| `message` | 私信操作 | 发送私信、回复私信 |
| `recommend` | 推荐操作 | 推荐视频、推荐用户 |
| `comment_like` | 评论点赞 | 对评论进行点赞 |
| `comment_reply` | 评论回复 | 回复他人评论 |

## 快速开始

### 初始化数据库

1. 运行数据库初始化脚本:
```bash
node init_mysql_tables.js
```

2. 验证数据库连接:
```bash
node check_user.js
```

### JavaScript 客户端示例

```javascript
// 批量提交任务数据
async function submitTaskData(deviceId, taskType, success, executionData) {
    const taskData = [{
        device_id: deviceId,
        device_name: `设备${deviceId}`,
        task_type: taskType,
        success: success,
        execution_data: executionData,
        timestamp: new Date().toISOString()
    }];
    
    try {
        const response = await fetch('http://*************:15001/wsRouter/batchTaskData', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ tasks: taskData })
        });
        
        const result = await response.json();
        console.log('提交结果:', result);
        return result;
    } catch (error) {
        console.error('提交失败:', error);
        throw error;
    }
}

// 使用示例
submitTaskData('device_001', 'like', true, {
    target_url: 'https://example.com/video/123',
    duration: 1500
});
```

### Python 客户端示例

```python
import requests
import json
from datetime import datetime

def submit_task_data(device_id, task_type, success, execution_data):
    url = 'http://*************:15001/wsRouter/batchTaskData'
    
    task_data = [{
        'device_id': device_id,
        'device_name': f'设备{device_id}',
        'task_type': task_type,
        'success': success,
        'execution_data': execution_data,
        'timestamp': datetime.now().isoformat()
    }]
    
    try:
        response = requests.post(url, json={'tasks': task_data})
        result = response.json()
        print('提交结果:', result)
        return result
    except Exception as e:
        print('提交失败:', e)
        raise e

# 使用示例
submit_task_data('device_001', 'like', True, {
    'target_url': 'https://example.com/video/123',
    'duration': 1500
})
```

## 监控页面

访问设备监控页面查看实时数据:
```
http://*************:15001/page/task/douyin514phoneNum.html
```

## 性能特性

- ✅ **高并发支持**: 支持同时处理多个设备的数据
- ✅ **事务保证**: 使用数据库事务确保数据一致性
- ✅ **错误隔离**: 单个任务失败不影响其他任务
- ✅ **实时监控**: 5秒自动刷新监控数据
- ✅ **连接池管理**: 自动管理数据库连接，支持20个并发连接

## 故障排除

### 常见问题

1. **连接被拒绝**: 检查服务器IP和端口是否正确
2. **数据库连接失败**: 验证数据库配置和权限
3. **数据格式错误**: 确保JSON格式正确，必填字段完整

### 错误码说明

- `0`: 操作成功
- `-1`: 操作失败，查看msg字段获取详细错误信息

## 联系支持

如有问题，请检查:
1. 服务器是否正常运行
2. 数据库连接是否正常
3. 网络连接是否畅通
4. 数据格式是否正确
